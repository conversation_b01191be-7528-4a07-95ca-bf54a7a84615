"use client";

import {
  Phone, MapPin, Clock, Mail, Instagram, Star,
  Camera, Heart, Users, Award, Menu, X, ChevronDown,
  ChevronLeft, ChevronRight, ZoomIn
} from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import ThemeToggle from "@/components/ui/ThemeToggle";
import Image from "next/image";

export default function MomentsByMagdalenaDemo() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [currentGalleryCategory, setCurrentGalleryCategory] = useState("all");
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [currentLightboxImage, setCurrentLightboxImage] = useState(0);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [bookingForm, setBookingForm] = useState({
    names: '',
    phone: '',
    email: '',
    weddingDate: '',
    location: '',
    budget: '',
    style: [] as string[],
    consultationType: 'online',
    additionalServices: [] as string[],
    message: ''
  });

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    setIsMenuOpen(false);
  };

  // Gallery categories
  const galleryCategories = [
    { id: "all", name: "Wszystkie zdjęcia" },
    { id: "ceremonies", name: "Ceremonie ślubne" },
    { id: "engagement", name: "Sesje narzeczeńskie" },
    { id: "reception", name: "Przyjęcia weselne" },
    { id: "portraits", name: "Portrety ślubne" },
    { id: "details", name: "Detale i dekoracje" },
    { id: "outdoor", name: "Plener i natura" }
  ];

  // Wedding photography portfolio images
  const portfolioImages = [
    {
      id: 1,
      src: "/images/photography/wedding-ceremony.jpg",
      alt: "Ceremonia ślubna w kościele - para młoda przy ołtarzu",
      category: "ceremonies",
      location: "Kościół św. Anny, Warszawa",
      style: "Reportażowy",
      package: "Premium"
    },
    {
      id: 2,
      src: "/images/photography/engagement-session.jpg",
      alt: "Romantyczna sesja narzeczeńska w parku",
      category: "engagement",
      location: "Park Łazienkowski, Warszawa",
      style: "Artystyczny",
      package: "Basic"
    },
    {
      id: 3,
      src: "/images/photography/first-dance.jpg",
      alt: "Pierwszy taniec pary młodej",
      category: "reception",
      location: "Hotel Bristol, Warszawa",
      style: "Klasyczny",
      package: "Luxury"
    },
    {
      id: 4,
      src: "/images/photography/bride-portrait.jpg",
      alt: "Portret panny młodej w sukni ślubnej",
      category: "portraits",
      location: "Studio fotograficzne",
      style: "Artystyczny",
      package: "Premium"
    },
    {
      id: 5,
      src: "/images/photography/wedding-rings.jpg",
      alt: "Obrączki ślubne na bukiecie",
      category: "details",
      location: "Pałac Jabłonna",
      style: "Klasyczny",
      package: "Basic"
    },
    {
      id: 6,
      src: "/images/photography/outdoor-session.jpg",
      alt: "Sesja plenerowa nad jeziorem",
      category: "outdoor",
      location: "Jezioro Zegrzyńskie",
      style: "Reportażowy",
      package: "Luxury"
    }
  ];

  // Filter portfolio images based on category
  const filteredImages = currentGalleryCategory === "all" 
    ? portfolioImages 
    : portfolioImages.filter(img => img.category === currentGalleryCategory);

  // Testimonials data
  const testimonials = [
    {
      id: 1,
      names: "Anna i Piotr",
      location: "Ślub w Pałacu Jabłonna",
      rating: 5,
      text: "Magdalena to nie tylko fotografka, to artystka! Nasze zdjęcia są absolutnie magiczne. Każdy szczegół, każda emocja została uwieczniona. Nie mogliśmy marzyć o lepszych wspomnieniach.",
      image: "https://images.unsplash.com/photo-1522673607200-164d1b6ce486?w=100&h=100&fit=crop&crop=faces"
    },
    {
      id: 2,
      names: "Kasia i Michał",
      location: "Ślub w Krakowie",
      rating: 5,
      text: "Profesjonalizm, dyskretność i niesamowite oko do detali. Magdalena sprawiła, że czuliśmy się swobodnie przez cały dzień. Zdjęcia przeszły nasze najśmielsze oczekiwania!",
      image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=faces"
    },
    {
      id: 3,
      names: "Marta i Tomasz",
      location: "Wesele w plenerze",
      rating: 5,
      text: "Najlepsza decyzja, jaką podjęliśmy przy organizacji ślubu! Magdalena uchwyciła emocje, których nawet my nie zauważyliśmy. Po roku nadal płaczemy oglądając te zdjęcia (ze szczęścia!).",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces"
    }
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <div className="min-h-screen theme-wedding-bg-primary theme-wedding-text-primary">
      {/* Navigation */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <button
                onClick={() => scrollToSection('hero')}
                className="text-xl font-bold text-gray-900 dark:text-white hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-200"
                style={{ fontFamily: 'Playfair Display, serif' }}
              >
                Moments by Magdalena
              </button>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex space-x-8">
              <button onClick={() => scrollToSection('portfolio')} className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Portfolio</button>
              <button onClick={() => scrollToSection('about')} className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">O mnie</button>
              <button onClick={() => scrollToSection('packages')} className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Pakiety</button>
              <button onClick={() => scrollToSection('process')} className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Proces</button>
              <button onClick={() => scrollToSection('testimonials')} className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Opinie</button>
              <button onClick={() => scrollToSection('contact')} className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Kontakt</button>
            </nav>

            {/* Theme Toggle & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <ThemeToggle />

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
                aria-label="Toggle menu"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden py-4 border-t border-gray-200 dark:border-gray-700 bg-white/95 dark:bg-gray-900/95">
              <div className="flex flex-col space-y-4">
                <button onClick={() => scrollToSection('portfolio')} className="text-left text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Portfolio</button>
                <button onClick={() => scrollToSection('about')} className="text-left text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">O mnie</button>
                <button onClick={() => scrollToSection('packages')} className="text-left text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Pakiety</button>
                <button onClick={() => scrollToSection('process')} className="text-left text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Proces</button>
                <button onClick={() => scrollToSection('testimonials')} className="text-left text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Opinie</button>
                <button onClick={() => scrollToSection('contact')} className="text-left text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">Kontakt</button>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image with Parallax Effect */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1519741497674-611481863552?w=1920&h=1080&fit=crop&crop=center"
            alt="Piękna ceremonia ślubna - para młoda w kościele"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6" style={{ fontFamily: 'Playfair Display, serif' }}>
            Uwieczniamy najpiękniejsze chwile
          </h1>
          <p className="text-xl sm:text-2xl text-white/90 mb-8 leading-relaxed">
            Fotografia ślubna pełna emocji • Artystyczne portfolio • 8 lat doświadczenia • Styl reportażowy i klasyczny
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={() => scrollToSection('portfolio')}
              className="px-8 py-4 theme-btn-wedding-primary rounded-lg text-lg font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
            >
              Zobacz portfolio
            </button>
            <button
              onClick={() => setIsBookingModalOpen(true)}
              className="px-8 py-4 theme-btn-wedding-secondary rounded-lg text-lg font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
            >
              Umów konsultację
            </button>
          </div>
          <div className="mt-12 text-white/80 italic text-lg" style={{ fontFamily: 'Playfair Display, serif' }}>
            &ldquo;Każda historia miłosna jest wyjątkowa&rdquo;
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="text-white/60" size={32} />
        </div>
      </section>

      {/* Demo Disclaimer */}
      <div className="theme-wedding-bg-accent py-3">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-sm theme-wedding-text-secondary">
            🎨 <strong>Demo Project</strong> - To jest projekt demonstracyjny stworzony przez <Link href="/" className="theme-wedding-accent-gold hover:underline font-semibold">Qualix Software</Link> jako przykład strony dla fotografa ślubnego
          </p>
        </div>
      </div>

      {/* Portfolio Gallery Section */}
      <section id="portfolio" className="py-20 theme-wedding-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-wedding-text-primary mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Portfolio
            </h2>
            <p className="text-xl theme-wedding-text-secondary max-w-3xl mx-auto">
              Odkryj magię uwiecznionych momentów. Każde zdjęcie opowiada unikalną historię miłości, pełną emocji i szczerych uczuć.
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {galleryCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setCurrentGalleryCategory(category.id)}
                className={`px-6 py-3 rounded-full transition-all duration-200 ${
                  currentGalleryCategory === category.id
                    ? 'theme-btn-wedding-primary'
                    : 'theme-wedding-bg-primary theme-wedding-text-secondary hover:theme-wedding-text-primary theme-wedding-border border'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredImages.map((image, index) => (
              <div
                key={image.id}
                className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => {
                  setCurrentLightboxImage(index);
                  setIsLightboxOpen(true);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    setCurrentLightboxImage(index);
                    setIsLightboxOpen(true);
                  }
                }}
                tabIndex={0}
                role="button"
                aria-label={`Otwórz zdjęcie: ${image.alt}`}
              >
                <div className="aspect-[4/3] relative">
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300"></div>

                  {/* Overlay Content */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-center text-white">
                      <ZoomIn size={32} className="mx-auto mb-2" />
                      <p className="text-sm font-medium">{image.location}</p>
                      <p className="text-xs opacity-80">{image.style}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* View More Button */}
          <div className="text-center mt-12">
            <button
              onClick={() => setIsBookingModalOpen(true)}
              className="px-8 py-4 theme-btn-wedding-secondary rounded-lg text-lg font-semibold hover:scale-105 transition-all duration-200"
            >
              Zobacz pełne portfolio
            </button>
          </div>
        </div>
      </section>

      {/* About Photographer Section */}
      <section id="about" className="py-20 theme-wedding-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div>
              <h2 className="text-4xl font-bold theme-wedding-text-primary mb-6" style={{ fontFamily: 'Playfair Display, serif' }}>
                Poznaj Magdalenę
              </h2>
              <p className="text-lg theme-wedding-text-secondary mb-6 italic">
                &ldquo;Artystka, która kocha uwieczniać miłość&rdquo;
              </p>

              <div className="space-y-6 theme-wedding-text-secondary">
                <p>
                  Jestem Magdalena, fotografka ślubna z 8-letnim doświadczeniem w uwiecznianiu najważniejszych momentów w życiu par. Moja filozofia to dyskretny reportaż połączony z artystyczną wizją.
                </p>
                <p>
                  Każdy ślub to unikalna historia miłosna, którą staram się opowiedzieć przez obiektyw mojego aparatu. Specjalizuję się w fotografii pełnej emocji, naturalnych momentów i szczerych uczuć.
                </p>
              </div>

              {/* Approach */}
              <div className="mt-8">
                <h3 className="text-xl font-semibold theme-wedding-text-primary mb-4">Podejście artystyczne:</h3>
                <ul className="space-y-2 theme-wedding-text-secondary">
                  <li className="flex items-start">
                    <Heart className="theme-wedding-accent-rose mr-3 mt-1 flex-shrink-0" size={16} />
                    Naturalny, reportażowy styl fotografii
                  </li>
                  <li className="flex items-start">
                    <Camera className="theme-wedding-accent-rose mr-3 mt-1 flex-shrink-0" size={16} />
                    Artystyczne portrety z wykorzystaniem światła
                  </li>
                  <li className="flex items-start">
                    <Users className="theme-wedding-accent-rose mr-3 mt-1 flex-shrink-0" size={16} />
                    Dyskretność i profesjonalne podejście
                  </li>
                  <li className="flex items-start">
                    <Award className="theme-wedding-accent-rose mr-3 mt-1 flex-shrink-0" size={16} />
                    Postprodukcja w ciepłym, romantycznym stylu
                  </li>
                </ul>
              </div>

              {/* Experience Stats */}
              <div className="mt-8 grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold theme-wedding-accent-gold">150+</div>
                  <div className="text-sm theme-wedding-text-secondary">Ślubów w portfolio</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold theme-wedding-accent-gold">8</div>
                  <div className="text-sm theme-wedding-text-secondary">lat doświadczenia</div>
                </div>
              </div>
            </div>

            {/* Image */}
            <div className="relative">
              <div className="aspect-[3/4] relative rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=600&h=800&fit=crop&crop=face"
                  alt="Magdalena - fotografka ślubna podczas pracy"
                  fill
                  className="object-cover"
                />
              </div>

              {/* Floating Quote */}
              <div className="absolute -bottom-6 -left-6 theme-wedding-bg-accent p-6 rounded-lg shadow-lg max-w-xs">
                <p className="text-sm theme-wedding-text-secondary italic">
                  &ldquo;Nie jestem tylko fotografką - jestem świadkiem Waszej miłości&rdquo;
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Package Pricing Section */}
      <section id="packages" className="py-20 theme-wedding-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-wedding-text-primary mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Pakiety fotograficzne
            </h2>
            <p className="text-xl theme-wedding-text-secondary max-w-3xl mx-auto">
              Wybierz pakiet idealnie dopasowany do Waszego wymarzonego ślubu. Każdy pakiet zawiera profesjonalną obsługę i najwyższą jakość zdjęć.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Basic Package */}
            <div className="theme-wedding-bg-primary rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold theme-wedding-text-primary mb-2">Basic Wedding</h3>
                <div className="text-4xl font-bold theme-wedding-accent-gold mb-2">2800 zł</div>
                <p className="theme-wedding-text-secondary">Idealny na początek</p>
              </div>

              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">6 godzin fotografii w dniu ślubu</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">300-400 zdjęć w pełnej rozdzielczości</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Profesjonalna korekta kolorystyczna</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Galeria online do udostępniania</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Pendrive z wszystkimi zdjęciami</span>
                </li>
                <li className="flex items-start text-sm opacity-60">
                  <X className="theme-wedding-text-secondary mr-3 mt-1 flex-shrink-0" size={16} />
                  <span className="theme-wedding-text-secondary">Sesja narzeczeńska</span>
                </li>
                <li className="flex items-start text-sm opacity-60">
                  <X className="theme-wedding-text-secondary mr-3 mt-1 flex-shrink-0" size={16} />
                  <span className="theme-wedding-text-secondary">Albumy i wydruki</span>
                </li>
              </ul>

              <button
                onClick={() => setIsBookingModalOpen(true)}
                className="w-full py-3 theme-btn-wedding-secondary rounded-lg font-semibold hover:scale-105 transition-all duration-200"
              >
                Wybierz Basic
              </button>
            </div>

            {/* Premium Package - Most Popular */}
            <div className="theme-wedding-bg-primary rounded-lg shadow-xl p-8 relative border-2 theme-wedding-accent-gold transform scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="theme-wedding-accent-gold px-4 py-1 rounded-full text-sm font-semibold theme-wedding-bg-primary">
                  ⭐ NAJPOPULARNIEJSZY
                </span>
              </div>

              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold theme-wedding-text-primary mb-2">Premium Wedding</h3>
                <div className="text-4xl font-bold theme-wedding-accent-gold mb-2">4200 zł</div>
                <p className="theme-wedding-text-secondary">Najczęściej wybierany</p>
              </div>

              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">8 godzin fotografii + przygotowania</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">500-600 zdjęć w pełnej rozdzielczości</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Sesja narzeczeńska (60 min)</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Profesjonalna galeria online</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">50 zdjęć w albumie 30x30cm</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Zapasowy fotograf w razie potrzeby</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Zdjęcia z drona (jeśli lokalizacja pozwala)</span>
                </li>
              </ul>

              <button
                onClick={() => setIsBookingModalOpen(true)}
                className="w-full py-3 theme-btn-wedding-primary rounded-lg font-semibold hover:scale-105 transition-all duration-200"
              >
                Wybierz Premium
              </button>
            </div>

            {/* Luxury Package */}
            <div className="theme-wedding-bg-primary rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold theme-wedding-text-primary mb-2">Luxury Wedding</h3>
                <div className="text-4xl font-bold theme-wedding-accent-gold mb-2">6500 zł</div>
                <p className="theme-wedding-text-secondary">Pełna obsługa premium</p>
              </div>

              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Całodniowa obsługa (10+ godzin)</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Drugi fotograf na ceremonii</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Sesja narzeczeńska + trash the dress</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">2 albumy (30x30cm + 20x20cm dla rodziców)</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">700-900 zdjęć w pełnej rozdzielczości</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Ekspresowa obróbka (30 zdjęć w 48h)</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 theme-wedding-accent-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="theme-wedding-text-secondary">Prawa do druku bez ograniczeń</span>
                </li>
              </ul>

              <button
                onClick={() => setIsBookingModalOpen(true)}
                className="w-full py-3 theme-btn-wedding-secondary rounded-lg font-semibold hover:scale-105 transition-all duration-200"
              >
                Wybierz Luxury
              </button>
            </div>
          </div>

          {/* Additional Services */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold theme-wedding-text-primary mb-8">Dodatki</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div className="theme-wedding-bg-primary p-4 rounded-lg shadow">
                <div className="font-semibold theme-wedding-text-primary">Sesja narzeczeńska</div>
                <div className="theme-wedding-accent-gold font-bold">600 zł</div>
              </div>
              <div className="theme-wedding-bg-primary p-4 rounded-lg shadow">
                <div className="font-semibold theme-wedding-text-primary">Drugi fotograf</div>
                <div className="theme-wedding-accent-gold font-bold">800 zł</div>
              </div>
              <div className="theme-wedding-bg-primary p-4 rounded-lg shadow">
                <div className="font-semibold theme-wedding-text-primary">Album dodatkowy 30x30</div>
                <div className="theme-wedding-accent-gold font-bold">400 zł</div>
              </div>
              <div className="theme-wedding-bg-primary p-4 rounded-lg shadow">
                <div className="font-semibold theme-wedding-text-primary">Zdjęcia z drona</div>
                <div className="theme-wedding-accent-gold font-bold">300 zł</div>
              </div>
              <div className="theme-wedding-bg-primary p-4 rounded-lg shadow">
                <div className="font-semibold theme-wedding-text-primary">Trash the dress</div>
                <div className="theme-wedding-accent-gold font-bold">400 zł</div>
              </div>
            </div>
          </div>

          {/* Payment Terms */}
          <div className="mt-12 text-center theme-wedding-bg-accent p-6 rounded-lg">
            <h4 className="font-semibold theme-wedding-text-primary mb-2">Warunki płatności</h4>
            <p className="theme-wedding-text-secondary">50% zaliczki przy podpisaniu umowy, 50% w dniu ślubu</p>
            <p className="theme-wedding-text-secondary">Zdjęcia gotowe w ciągu 6-8 tygodni</p>
          </div>
        </div>
      </section>

      {/* Wedding Process Section */}
      <section id="process" className="py-20 theme-wedding-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-wedding-text-primary mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Jak przebiega współpraca?
            </h2>
            <p className="text-xl theme-wedding-text-secondary max-w-3xl mx-auto">
              Od pierwszego kontaktu do dostarczenia zdjęć - poznaj każdy krok naszej wspólnej podróży
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="w-16 h-16 theme-wedding-accent-gold rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                1
              </div>
              <h3 className="text-xl font-bold theme-wedding-text-primary mb-4">Poznanie</h3>
              <p className="theme-wedding-text-secondary mb-4">
                Spotykamy się na kawie (online lub osobiście), żeby omówić Wasze oczekiwania, styl fotografii i szczegóły ślubu.
              </p>
              <div className="text-sm theme-wedding-accent-gold font-semibold">
                Czas: 1-2 godziny • Koszt: bezpłatny
              </div>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="w-16 h-16 theme-wedding-accent-gold rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                2
              </div>
              <h3 className="text-xl font-bold theme-wedding-text-primary mb-4">Rezerwacja terminu</h3>
              <p className="theme-wedding-text-secondary mb-4">
                Po podjęciu decyzji podpisujemy umowę i przyjmuję zaliczkę 50%. Wasz termin jest zarezerwowany tylko dla Was.
              </p>
              <div className="text-sm theme-wedding-accent-gold font-semibold">
                Wymagane: umowa + 50% zaliczki
              </div>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="w-16 h-16 theme-wedding-accent-gold rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                3
              </div>
              <h3 className="text-xl font-bold theme-wedding-text-primary mb-4">Sesja narzeczeńska</h3>
              <p className="theme-wedding-text-secondary mb-4">
                Idealna okazja do poznania się przed ślubem. Zdjęcia mogą posłużyć do zaproszeń lub dekoracji sali.
              </p>
              <div className="text-sm theme-wedding-accent-gold font-semibold">
                Czas: 1-2 godziny • Opcjonalna
              </div>
            </div>

            {/* Step 4 */}
            <div className="text-center">
              <div className="w-16 h-16 theme-wedding-accent-gold rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                4
              </div>
              <h3 className="text-xl font-bold theme-wedding-text-primary mb-4">Finalne uzgodnienia</h3>
              <p className="theme-wedding-text-secondary mb-4">
                2 tygodnie przed ślubem omawiamy szczegóły: timeline, ważne osoby, specjalne momenty, lokalizacje.
              </p>
              <div className="text-sm theme-wedding-accent-gold font-semibold">
                Forma: spotkanie lub telefon
              </div>
            </div>

            {/* Step 5 */}
            <div className="text-center">
              <div className="w-16 h-16 theme-wedding-accent-gold rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                5
              </div>
              <h3 className="text-xl font-bold theme-wedding-text-primary mb-4">Dzień ślubu</h3>
              <p className="theme-wedding-text-secondary mb-4">
                Jestem z Wami od przygotowań do końca przyjęcia, dyskretnie dokumentując każdy ważny moment.
              </p>
              <div className="text-sm theme-wedding-accent-gold font-semibold">
                Dokumentacja: ceremonia + przyjęcie
              </div>
            </div>

            {/* Step 6 */}
            <div className="text-center">
              <div className="w-16 h-16 theme-wedding-accent-gold rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                6
              </div>
              <h3 className="text-xl font-bold theme-wedding-text-primary mb-4">Obróbka i dostawa</h3>
              <p className="theme-wedding-text-secondary mb-4">
                W ciągu 6-8 tygodni otrzymujecie kompletną galerię. Najpierw sneak peek (10 zdjęć w 48h).
              </p>
              <div className="text-sm theme-wedding-accent-gold font-semibold">
                Dostawa: galeria online + pendrive/albumy
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 theme-wedding-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-wedding-text-primary mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Co mówią o mnie pary
            </h2>
            <p className="text-xl theme-wedding-text-secondary max-w-3xl mx-auto">
              Najpiękniejsze słowa od par, które zaufały mi uwiecznienie swojego najważniejszego dnia
            </p>
          </div>

          <div className="relative max-w-4xl mx-auto">
            {/* Testimonial Card */}
            {testimonials[currentTestimonial] && (
              <div className="theme-wedding-bg-primary rounded-lg shadow-xl p-8 md:p-12">
                <div className="flex flex-col md:flex-row items-center gap-8">
                  {/* Couple Photo */}
                  <div className="flex-shrink-0">
                    <div className="w-24 h-24 rounded-full overflow-hidden">
                      <Image
                        src={testimonials[currentTestimonial].image}
                        alt={`Zdjęcie pary ${testimonials[currentTestimonial].names}`}
                        width={96}
                        height={96}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </div>

                  {/* Testimonial Content */}
                  <div className="flex-1 text-center md:text-left">
                    {/* Stars */}
                    <div className="flex justify-center md:justify-start mb-4">
                      {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                        <Star key={i} className="theme-wedding-accent-gold fill-current" size={20} />
                      ))}
                    </div>

                    {/* Quote */}
                    <blockquote className="text-lg theme-wedding-text-secondary italic mb-6">
                      &ldquo;{testimonials[currentTestimonial].text}&rdquo;
                    </blockquote>

                    {/* Names and Location */}
                    <div>
                      <div className="font-bold theme-wedding-text-primary text-xl">
                        {testimonials[currentTestimonial].names}
                      </div>
                      <div className="theme-wedding-text-secondary">
                        {testimonials[currentTestimonial].location}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Dots */}
            <div className="flex justify-center mt-8 space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-200 ${
                    index === currentTestimonial
                      ? 'theme-wedding-accent-gold'
                      : 'theme-wedding-text-secondary opacity-30'
                  }`}
                />
              ))}
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={() => setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 p-2 theme-wedding-bg-primary rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <ChevronLeft className="theme-wedding-text-secondary" size={24} />
            </button>
            <button
              onClick={() => setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 p-2 theme-wedding-bg-primary rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <ChevronRight className="theme-wedding-text-secondary" size={24} />
            </button>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold theme-wedding-accent-gold mb-2">150+</div>
              <div className="theme-wedding-text-secondary">Szczęśliwych par</div>
            </div>
            <div>
              <div className="text-4xl font-bold theme-wedding-accent-gold mb-2">5.0</div>
              <div className="theme-wedding-text-secondary">Średnia ocen</div>
            </div>
            <div>
              <div className="text-4xl font-bold theme-wedding-accent-gold mb-2">8</div>
              <div className="theme-wedding-text-secondary">Lat doświadczenia</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact & Booking Section */}
      <section id="contact" className="py-20 theme-wedding-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-wedding-text-primary mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Umów bezpłatną konsultację
            </h2>
            <p className="text-xl theme-wedding-text-secondary max-w-3xl mx-auto">
              Opowiedz o swoim ślubie, poznajmy się i zaplanujmy współpracę. Każda para otrzymuje mały prezent na koniec spotkania 💝
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Info */}
            <div>
              <h3 className="text-2xl font-bold theme-wedding-text-primary mb-8">Skontaktuj się ze mną</h3>

              <div className="space-y-6">
                <div className="flex items-start">
                  <MapPin className="theme-wedding-accent-gold mr-4 mt-1 flex-shrink-0" size={20} />
                  <div>
                    <div className="font-semibold theme-wedding-text-primary">Studio</div>
                    <div className="theme-wedding-text-secondary">ul. Krucza 24/26, 00-526 Warszawa</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="theme-wedding-accent-gold mr-4 mt-1 flex-shrink-0" size={20} />
                  <div>
                    <div className="font-semibold theme-wedding-text-primary">Telefon</div>
                    <div className="theme-wedding-text-secondary">+48 555 678 901</div>
                    <div className="text-sm theme-wedding-text-secondary">Najlepiej Wt-Pt 10:00-18:00</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Mail className="theme-wedding-accent-gold mr-4 mt-1 flex-shrink-0" size={20} />
                  <div>
                    <div className="font-semibold theme-wedding-text-primary">Email</div>
                    <div className="theme-wedding-text-secondary"><EMAIL></div>
                    <div className="text-sm theme-wedding-text-secondary">Odpowiedzi w ciągu 24 godzin</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="theme-wedding-accent-gold mr-4 mt-1 flex-shrink-0" size={20} />
                  <div>
                    <div className="font-semibold theme-wedding-text-primary">Konsultacje</div>
                    <div className="theme-wedding-text-secondary">Wtorek - Sobota (po umówieniu)</div>
                    <div className="text-sm theme-wedding-text-secondary">Online lub w centrum Warszawy</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Instagram className="theme-wedding-accent-gold mr-4 mt-1 flex-shrink-0" size={20} />
                  <div>
                    <div className="font-semibold theme-wedding-text-primary">Instagram</div>
                    <div className="theme-wedding-text-secondary">@moments_by_magdalena</div>
                    <div className="text-sm theme-wedding-text-secondary">Codzienne inspiracje ślubne</div>
                  </div>
                </div>
              </div>

              {/* Service Areas */}
              <div className="mt-8 p-6 theme-wedding-bg-accent rounded-lg">
                <h4 className="font-semibold theme-wedding-text-primary mb-2">Zasięg działania</h4>
                <p className="theme-wedding-text-secondary text-sm">
                  Głównie Warszawa i okolice, ale podróżuję po całej Polsce. Ślub za granicą? Oczywiście! Uwielbiamy sesje w wyjątkowych miejscach.
                </p>
              </div>
            </div>

            {/* Quick Contact Form */}
            <div className="theme-wedding-bg-secondary p-8 rounded-lg">
              <h3 className="text-2xl font-bold theme-wedding-text-primary mb-6">Szybki kontakt</h3>

              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Imiona pary *
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 theme-wedding-bg-primary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    placeholder="Anna i Piotr"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-3 theme-wedding-bg-primary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Telefon
                  </label>
                  <input
                    type="tel"
                    className="w-full px-4 py-3 theme-wedding-bg-primary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    placeholder="+48 123 456 789"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Data ślubu (jeśli znana)
                  </label>
                  <input
                    type="date"
                    className="w-full px-4 py-3 theme-wedding-bg-primary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Krótka wiadomość *
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-4 py-3 theme-wedding-bg-primary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    placeholder="Opowiedz o swoim ślubie..."
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full py-3 theme-btn-wedding-primary rounded-lg font-semibold hover:scale-105 transition-all duration-200"
                >
                  Wyślij wiadomość
                </button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-sm theme-wedding-text-secondary">
                  📅 Konsultacje: Wt-Sob, online lub w centrum Warszawy<br/>
                  ⏱️ Czas trwania: 60-90 minut<br/>
                  ☕ Kawa/herbata za mną 😊
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="theme-wedding-bg-accent py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="text-2xl font-bold theme-wedding-text-primary mb-4" style={{ fontFamily: 'Playfair Display, serif' }}>
              Moments by Magdalena
            </div>
            <p className="theme-wedding-text-secondary mb-6">
              Profesjonalna fotografia ślubna • Warszawa i okolice • 8 lat doświadczenia
            </p>

            <div className="flex justify-center space-x-6 mb-8">
              <a href="tel:+48555678901" className="theme-wedding-text-secondary hover:theme-wedding-accent-gold transition-colors duration-200">
                <Phone size={24} />
              </a>
              <a href="mailto:<EMAIL>" className="theme-wedding-text-secondary hover:theme-wedding-accent-gold transition-colors duration-200">
                <Mail size={24} />
              </a>
              <a href="https://instagram.com/moments_by_magdalena" className="theme-wedding-text-secondary hover:theme-wedding-accent-gold transition-colors duration-200" target="_blank" rel="noopener noreferrer">
                <Instagram size={24} />
              </a>
            </div>

            <div className="theme-wedding-border border-t pt-6">
              <p className="text-sm theme-wedding-text-secondary">
                © 2024 Moments by Magdalena. Wszystkie prawa zastrzeżone.
              </p>
              <p className="text-xs theme-wedding-text-secondary mt-2">
                🎨 <strong>Demo Project</strong> - Projekt demonstracyjny stworzony przez{' '}
                <Link href="/" className="theme-wedding-accent-gold hover:underline font-semibold">
                  Qualix Software
                </Link>
              </p>
            </div>
          </div>
        </div>
      </footer>

      {/* Lightbox Modal */}
      {isLightboxOpen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            {/* Close Button */}
            <button
              onClick={() => setIsLightboxOpen(false)}
              className="absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors duration-200"
            >
              <X size={24} />
            </button>

            {/* Image */}
            <div className="relative">
              <Image
                src={filteredImages[currentLightboxImage]?.src || ''}
                alt={filteredImages[currentLightboxImage]?.alt || ''}
                width={800}
                height={600}
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
              />

              {/* Image Info Overlay */}
              <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-4 rounded-b-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold mb-1">{filteredImages[currentLightboxImage]?.location}</h3>
                    <p className="text-sm opacity-80">{filteredImages[currentLightboxImage]?.style} • {filteredImages[currentLightboxImage]?.package}</p>
                  </div>
                  <button
                    onClick={() => setIsBookingModalOpen(true)}
                    className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm font-semibold transition-colors duration-200"
                  >
                    Zapytaj o podobny styl
                  </button>
                </div>
              </div>
            </div>

            {/* Navigation Arrows */}
            {filteredImages.length > 1 && (
              <>
                <button
                  onClick={() => setCurrentLightboxImage((prev) => (prev - 1 + filteredImages.length) % filteredImages.length)}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors duration-200"
                >
                  <ChevronLeft size={24} />
                </button>
                <button
                  onClick={() => setCurrentLightboxImage((prev) => (prev + 1) % filteredImages.length)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors duration-200"
                >
                  <ChevronRight size={24} />
                </button>
              </>
            )}

            {/* Image Counter */}
            <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {currentLightboxImage + 1} / {filteredImages.length}
            </div>
          </div>
        </div>
      )}

      {/* Booking Modal */}
      {isBookingModalOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="theme-wedding-bg-primary rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold theme-wedding-text-primary">Umów bezpłatną konsultację</h2>
                <button
                  onClick={() => setIsBookingModalOpen(false)}
                  className="p-2 theme-wedding-text-secondary hover:theme-wedding-text-primary transition-colors duration-200"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Booking Form */}
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                      Imiona pary *
                    </label>
                    <input
                      type="text"
                      value={bookingForm.names}
                      onChange={(e) => setBookingForm({...bookingForm, names: e.target.value})}
                      className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      placeholder="Anna i Piotr"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      value={bookingForm.phone}
                      onChange={(e) => setBookingForm({...bookingForm, phone: e.target.value})}
                      className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      placeholder="+48 123 456 789"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    value={bookingForm.email}
                    onChange={(e) => setBookingForm({...bookingForm, email: e.target.value})}
                    className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                      Data ślubu *
                    </label>
                    <input
                      type="date"
                      value={bookingForm.weddingDate}
                      onChange={(e) => setBookingForm({...bookingForm, weddingDate: e.target.value})}
                      className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                      Lokalizacja ślubu *
                    </label>
                    <input
                      type="text"
                      value={bookingForm.location}
                      onChange={(e) => setBookingForm({...bookingForm, location: e.target.value})}
                      className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      placeholder="Warszawa"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Planowany budżet na fotografię
                  </label>
                  <select
                    value={bookingForm.budget}
                    onChange={(e) => setBookingForm({...bookingForm, budget: e.target.value})}
                    className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  >
                    <option value="">Wybierz przedział</option>
                    <option value="do-3000">do 3000 zł</option>
                    <option value="3000-5000">3000-5000 zł</option>
                    <option value="5000-7000">5000-7000 zł</option>
                    <option value="powyżej-7000">powyżej 7000 zł</option>
                    <option value="nie-jestem-pewna">nie jestem pewna</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Preferowana forma konsultacji
                  </label>
                  <div className="flex flex-wrap gap-4">
                    {['online', 'kawiarnia', 'telefon'].map((type) => (
                      <label key={type} className="flex items-center">
                        <input
                          type="radio"
                          name="consultationType"
                          value={type}
                          checked={bookingForm.consultationType === type}
                          onChange={(e) => setBookingForm({...bookingForm, consultationType: e.target.value})}
                          className="mr-2"
                        />
                        <span className="theme-wedding-text-secondary capitalize">
                          {type === 'kawiarnia' ? 'Spotkanie w kawiarni' : type === 'online' ? 'Online' : 'Telefon'}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-wedding-text-primary mb-2">
                    Opowiedz o swoim ślubie *
                  </label>
                  <textarea
                    rows={4}
                    value={bookingForm.message}
                    onChange={(e) => setBookingForm({...bookingForm, message: e.target.value})}
                    className="w-full px-4 py-3 theme-wedding-bg-secondary theme-wedding-text-primary theme-wedding-border border rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    placeholder="Opowiedz o swoich planach, oczekiwaniach, stylu ślubu..."
                    required
                  ></textarea>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsBookingModalOpen(false)}
                    className="flex-1 py-3 theme-btn-wedding-secondary rounded-lg font-semibold hover:scale-105 transition-all duration-200"
                  >
                    Anuluj
                  </button>
                  <button
                    type="submit"
                    className="flex-1 py-3 theme-btn-wedding-primary rounded-lg font-semibold hover:scale-105 transition-all duration-200"
                  >
                    Umów konsultację
                  </button>
                </div>
              </form>

              {/* Consultation Details */}
              <div className="mt-6 p-4 theme-wedding-bg-accent rounded-lg">
                <h4 className="font-semibold theme-wedding-text-primary mb-2">Po wypełnieniu formularza otrzymacie:</h4>
                <ul className="text-sm theme-wedding-text-secondary space-y-1">
                  <li>✓ Potwierdzenie mailowe w ciągu 2 godzin</li>
                  <li>✓ Link do wyboru konkretnego terminu</li>
                  <li>✓ Przewodnik przygotowań do konsultacji</li>
                  <li>✓ Dostęp do pełnego portfolio online</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}
