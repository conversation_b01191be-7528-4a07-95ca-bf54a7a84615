'use client';

import { useState, useEffect } from 'react';
import { Menu, X, Phone, MapPin, Clock, Star, Users, Dumbbell, Heart, Calendar, User, Zap, Target, Check, Crown, Shield, Award, Instagram, TrendingUp, Scale, Timer, Home, Wifi, Car, Coffee } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import { FitnessClass } from './types';

export default function FlexFitStudioPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('Wszystkie zajęcia');
  const [selectedDay, setSelectedDay] = useState('Poniedziałek');
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [selectedClass, setSelectedClass] = useState<(Omit<FitnessClass, 'schedule'> & { schedule: FitnessClass['schedule'][0] }) | null>(null);
  const [bookingStep, setBookingStep] = useState(1);
  const [showMembershipModal, setShowMembershipModal] = useState(false);
  const [selectedMembership, setSelectedMembership] = useState<typeof membershipTiers[0] | null>(null);
  const [membershipStep, setMembershipStep] = useState(1);
  const [showTrainerModal, setShowTrainerModal] = useState(false);
  const [selectedTrainer, setSelectedTrainer] = useState<typeof trainers[0] | null>(null);

  // Sample fitness classes data
  const fitnessClasses: FitnessClass[] = [
    {
      id: '1',
      name: 'Morning HIIT',
      description: 'Intensywny trening cardio z elementami siłowymi. Spalanie tłuszczu i budowanie kondycji.',
      duration: 45,
      difficulty: 'Średnio zaawansowany',
      category: 'Cardio & HIIT',
      trainer: 'Anna Kowalska',
      maxParticipants: 15,
      currentParticipants: 12,
      equipment: ['Mat', 'Dumbbells', 'Kettlebell'],
      caloriesBurned: '400-500 kcal',
      schedule: [
        { id: 's1', classId: '1', dayOfWeek: 'Poniedziałek', startTime: '06:00', endTime: '06:45', isAvailable: true },
        { id: 's2', classId: '1', dayOfWeek: 'Środa', startTime: '06:00', endTime: '06:45', isAvailable: true },
        { id: 's3', classId: '1', dayOfWeek: 'Piątek', startTime: '06:00', endTime: '06:45', isAvailable: true }
      ]
    },
    {
      id: '2',
      name: 'Yoga Flow',
      description: 'Płynne sekwencje jogi łączące oddech z ruchem. Elastyczność i spokój umysłu.',
      duration: 60,
      difficulty: 'Początkujący',
      category: 'Yoga & Stretching',
      trainer: 'Marta Zielińska',
      maxParticipants: 20,
      currentParticipants: 18,
      equipment: ['Mat', 'Bloki', 'Pasy'],
      caloriesBurned: '200-300 kcal',
      schedule: [
        { id: 's4', classId: '2', dayOfWeek: 'Poniedziałek', startTime: '07:00', endTime: '08:00', isAvailable: true },
        { id: 's5', classId: '2', dayOfWeek: 'Wtorek', startTime: '18:30', endTime: '19:30', isAvailable: true },
        { id: 's6', classId: '2', dayOfWeek: 'Czwartek', startTime: '07:00', endTime: '08:00', isAvailable: true }
      ]
    },
    {
      id: '3',
      name: 'CrossFit WOD',
      description: 'Workout of the Day - funkcjonalny trening siłowo-kondycyjny. Dla prawdziwych wojowników!',
      duration: 50,
      difficulty: 'Zaawansowany',
      category: 'Siła & CrossFit',
      trainer: 'Michał Pawlak',
      maxParticipants: 12,
      currentParticipants: 12,
      equipment: ['Barbell', 'Box', 'Kettlebell', 'Pull-up bar'],
      caloriesBurned: '500-700 kcal',
      schedule: [
        { id: 's7', classId: '3', dayOfWeek: 'Poniedziałek', startTime: '18:00', endTime: '18:50', isAvailable: false, waitingList: 3 },
        { id: 's8', classId: '3', dayOfWeek: 'Środa', startTime: '18:00', endTime: '18:50', isAvailable: true },
        { id: 's9', classId: '3', dayOfWeek: 'Piątek', startTime: '18:00', endTime: '18:50', isAvailable: true }
      ]
    },
    {
      id: '4',
      name: 'Zumba Party',
      description: 'Taneczne cardio pełne energii! Spalaj kalorie bawiąc się przy latynoskich rytmach.',
      duration: 45,
      difficulty: 'Początkujący',
      category: 'Taniec & Zumba',
      trainer: 'Kasia Malinowska',
      maxParticipants: 25,
      currentParticipants: 20,
      equipment: ['Tylko dobry humor!'],
      caloriesBurned: '350-450 kcal',
      schedule: [
        { id: 's10', classId: '4', dayOfWeek: 'Poniedziałek', startTime: '19:00', endTime: '19:45', isAvailable: true },
        { id: 's11', classId: '4', dayOfWeek: 'Wtorek', startTime: '19:00', endTime: '19:45', isAvailable: true },
        { id: 's12', classId: '4', dayOfWeek: 'Czwartek', startTime: '19:00', endTime: '19:45', isAvailable: true }
      ]
    },
    {
      id: '5',
      name: 'Strength Training',
      description: 'Klasyczny trening siłowy z wolnymi ciężarami. Buduj masę mięśniową i siłę.',
      duration: 60,
      difficulty: 'Średnio zaawansowany',
      category: 'Siła & CrossFit',
      trainer: 'Tomasz Wiśniewski',
      maxParticipants: 10,
      currentParticipants: 8,
      equipment: ['Barbell', 'Dumbbells', 'Bench'],
      caloriesBurned: '300-400 kcal',
      schedule: [
        { id: 's13', classId: '5', dayOfWeek: 'Poniedziałek', startTime: '20:00', endTime: '21:00', isAvailable: true },
        { id: 's14', classId: '5', dayOfWeek: 'Środa', startTime: '20:00', endTime: '21:00', isAvailable: true },
        { id: 's15', classId: '5', dayOfWeek: 'Piątek', startTime: '20:00', endTime: '21:00', isAvailable: true }
      ]
    }
  ];

  const filters = ['Wszystkie zajęcia', 'Cardio & HIIT', 'Siła & CrossFit', 'Yoga & Stretching', 'Taniec & Zumba', 'Początkujący', 'Zaawansowany'];
  const days = ['Poniedziałek', 'Wtorek', 'Środa', 'Czwartek', 'Piątek', 'Sobota', 'Niedziela'];

  // Membership tiers data
  const membershipTiers = [
    {
      id: 'basic',
      name: 'BASIC',
      price: 89,
      period: 'miesiąc',
      isPopular: false,
      description: 'Idealny start dla początkujących',
      features: [
        'Dostęp do siłowni 6:00-16:00',
        'Podstawowy sprzęt cardio i siłowy',
        'Szatnia i prysznice',
        'Parking gratis',
        'Wprowadzenie do treningu'
      ],
      restrictions: [
        'Brak dostępu do zajęć grupowych',
        'Brak dostępu wieczornego',
        'Brak sauny'
      ],
      color: 'basic',
      icon: Shield
    },
    {
      id: 'premium',
      name: 'PREMIUM',
      price: 149,
      period: 'miesiąc',
      isPopular: true,
      description: 'Najpopularniejszy wybór naszych członków',
      features: [
        'Pełny dostęp 6:00-23:00',
        'Wszystkie zajęcia grupowe',
        'Nowoczesny sprzęt i strefa funkcjonalna',
        'Sauna i strefa relaksu',
        'Plan treningowy + konsultacja',
        'Parking + ręcznik gratis',
        'Priorytet w rezerwacjach'
      ],
      restrictions: [],
      color: 'premium',
      icon: Star
    },
    {
      id: 'vip',
      name: 'VIP',
      price: 229,
      period: 'miesiąc',
      isPopular: false,
      description: 'Maksymalny komfort i indywidualne podejście',
      features: [
        'Wszystko z Premium +',
        'Treningi personalne (2x/miesiąc)',
        'Analiza składu ciała',
        'Plan żywienia i suplementacji',
        'Priorytet w zapisach na zajęcia',
        'Gość gratis (2x/miesiąc)',
        'Masaż sportowy (1x/miesiąc)',
        'Dedykowany doradca fitness'
      ],
      restrictions: [],
      color: 'vip',
      icon: Crown
    }
  ];

  // Trainer profiles data
  const trainers = [
    {
      id: '1',
      name: 'Anna Kowalska',
      title: 'Head Trainer & Specjalistka HIIT',
      specializations: ['HIIT', 'Tabata', 'Trening siłowy dla kobiet', 'Spalanie tłuszczu'],
      experience: '6 lat doświadczenia w fitness',
      certifications: ['NASM-CPT', 'TRX Certified', 'Kettlebell Level 2'],
      description: 'Specjalizuje się w spalaniu tłuszczu i treningu funkcjonalnym. Pomogła setkom kobiet osiągnąć wymarzoną sylwetkę.',
      image: '/trainers/anna-kowalska.jpg',
      socialMedia: {
        instagram: '@anna_flexfit'
      },
      achievements: [
        '2,5 tys. obserwujących na Instagram',
        'Certyfikowany trener personalny',
        'Specjalistka od treningu kobiet'
      ],
      personalRecords: {
        'Deadlift': '120kg',
        'Squat': '100kg',
        'Bench Press': '70kg'
      }
    },
    {
      id: '2',
      name: 'Michał Pawlak',
      title: 'Trener CrossFit & Powerlifting',
      specializations: ['CrossFit', 'Powerlifting', 'Trening siły', 'Trening kondycji'],
      experience: '8 lat w sporcie siłowym',
      certifications: ['CrossFit Level 2', 'Powerlifting Coach', 'Olympic Weightlifting'],
      description: 'Były powerlifter, teraz pomaga innym osiągać cele siłowe. Specjalista od treningu funkcjonalnego i CrossFit.',
      image: '/trainers/michal-pawlak.jpg',
      socialMedia: {
        instagram: '@michal_crossfit'
      },
      achievements: [
        'Były zawodnik powerlifting',
        'Mistrz Polski w kategorii -83kg',
        'Certyfikowany trener CrossFit'
      ],
      personalRecords: {
        'Deadlift': '250kg',
        'Squat': '200kg',
        'Bench Press': '160kg'
      }
    },
    {
      id: '3',
      name: 'Marta Zielińska',
      title: 'Ekspertka Yoga & Wellness',
      specializations: ['Hatha Yoga', 'Yin Yoga', 'Mindfulness', 'Medytacja'],
      experience: '10 lat praktyki jogi',
      certifications: ['RYT-500', 'Yin Yoga Certified', 'Instruktor medytacji'],
      description: 'Holistyczne podejście do zdrowia ciała i umysłu. Łączy tradycyjną jogę z nowoczesnymi technikami wellness.',
      image: '/trainers/marta-zielinska.jpg',
      socialMedia: {
        instagram: '@marta_yoga_wellness'
      },
      achievements: [
        'Wykształcenie: Fizjoterapia',
        'Studia podyplomowe z dietetyki',
        'Certyfikowany instruktor mindfulness'
      ],
      personalRecords: {
        'Flexibility Score': '95/100',
        'Balance Test': 'Expert Level',
        'Meditation': '2h daily practice'
      }
    },
    {
      id: '4',
      name: 'Kasia Malinowska',
      title: 'Instruktorka Tańca & Cardio',
      specializations: ['Zumba', 'Dance Cardio', 'HIIT Dance', 'Aerobik'],
      experience: '12 lat w tańcu i fitness',
      certifications: ['Zumba Gold', 'Strong by Zumba', 'Dance Fitness Instructor'],
      description: 'Taniec to najlepsza forma cardio - zabawa + efekty! Energia i pozytywne wibracje gwarantowane.',
      image: '/trainers/kasia-malinowska.jpg',
      socialMedia: {
        instagram: '@kasia_dance_fitness'
      },
      achievements: [
        '10 lat tańca towarzyskiego',
        'Instruktor aerobiku',
        'Specjalistka dance fitness'
      ],
      personalRecords: {
        'Dance Competitions': '15 nagród',
        'Classes per week': '20+',
        'Energy Level': 'Maximum!'
      }
    },
    {
      id: '5',
      name: 'Tomasz Wiśniewski',
      title: 'Specjalista Treningów Personalnych',
      specializations: ['Personal Training', 'Profilaktyka kontuzji', 'Wydolność sportowa', 'Rehabilitacja'],
      experience: '8 lat w fitness i sporcie',
      certifications: ['ACSM-CPT', 'Trening korekcyjny', 'Doradca żywieniowy'],
      description: 'Indywidualne podejście do każdego celu fitness. Specjalista od bezpiecznego treningu i profilaktyki kontuzji.',
      image: '/trainers/tomasz-wisniewski.jpg',
      socialMedia: {
        instagram: '@tomasz_personal_trainer'
      },
      achievements: [
        'Były profesjonalny sportowiec',
        'Certyfikowany fizjoterapeuta',
        'Specjalista żywienia sportowego'
      ],
      personalRecords: {
        'Clients Trained': '200+',
        'Success Rate': '95%',
        'Injury Prevention': 'Expert'
      }
    }
  ];

  // Transformation stories data
  const transformationStories = [
    {
      id: '1',
      memberName: 'Kasia',
      age: 28,
      timeframe: '8 miesięcy',
      membershipType: 'Premium',
      before: {
        weight: '74 kg',
        description: 'Brak kondycji, stresująca praca, niska energia'
      },
      after: {
        weight: '62 kg',
        description: 'Silna i pewna siebie, pełna energii'
      },
      quote: 'FlexFit zmienił nie tylko moje ciało, ale całe życie. Zespół traktuje mnie jak rodzinę!',
      achievements: [
        '-12 kg wagi',
        '+150% kondycji',
        'Nowa pewność siebie',
        'Zdrowe nawyki żywieniowe'
      ]
    },
    {
      id: '2',
      memberName: 'Marcin',
      age: 35,
      timeframe: '12 miesięcy',
      membershipType: 'VIP + treningi personalne',
      before: {
        weight: '95 kg',
        description: 'Ból pleców, siedzący tryb życia, brak energii'
      },
      after: {
        weight: '78 kg',
        description: 'Zdrowe plecy, aktywny styl życia, pełen energii'
      },
      quote: 'Myślałem, że jestem za stary, żeby zaczynać. Teraz jestem w najlepszej formie w życiu!',
      achievements: [
        '-17 kg wagi',
        'Koniec bólu pleców',
        'Pierwszy maraton ukończony',
        'Nowy styl życia'
      ]
    },
    {
      id: '3',
      memberName: 'Asia',
      age: 42,
      timeframe: '6 miesięcy',
      membershipType: 'Premium (głównie yoga + siła)',
      before: {
        weight: 'Nie o wagę chodziło',
        description: 'Depresja poporodowa, brak energii, niska samoocena'
      },
      after: {
        weight: 'Silna mama',
        description: 'Pewna siebie, energiczna, szczęśliwa'
      },
      quote: 'Odnalazłam siebie. Wróciła nie tylko forma, ale i pewność siebie. Jestem lepszą mamą.',
      achievements: [
        'Pokonana depresja',
        'Powrót pewności siebie',
        'Nowa energia do życia',
        'Inspiracja dla innych mam'
      ]
    },
    {
      id: '4',
      memberName: 'Paweł',
      age: 24,
      timeframe: '10 miesięcy',
      membershipType: 'CrossFit + plan żywienia',
      before: {
        weight: '65 kg',
        description: 'Chudy, bał się siłowni, brak mięśni'
      },
      after: {
        weight: '80 kg',
        description: '15 kg masy mięśniowej, pewny siebie, silny'
      },
      quote: 'Od zera do bohatera. CrossFit nauczył mnie, że granice są tylko w głowie.',
      achievements: [
        '+15 kg masy mięśniowej',
        'Pierwszy pull-up wykonany',
        'Deadlift 150kg osiągnięty',
        'Nowa pewność siebie'
      ]
    }
  ];

  // Facility areas data
  const facilityAreas = [
    {
      id: '1',
      name: 'Główna Sala Treningowa',
      description: '500m² przestrzeni treningowej z najnowszym sprzętem',
      size: '500m²',
      features: [
        'Najnowszy sprzęt Life Fitness',
        'Strefa cardio z TV i klimatyzacją',
        'Free weights do 50kg',
        'Functional training area',
        'Profesjonalne oświetlenie LED'
      ],
      equipment: ['Bieżnie', 'Rowery stacjonarne', 'Eliptyki', 'Hantle', 'Sztangi'],
      capacity: 50,
      specialNotes: 'Dostępna 24/7 dla członków Premium i VIP'
    },
    {
      id: '2',
      name: 'Sale do Zajęć Grupowych',
      description: '2 w pełni wyposażone sale do różnych form aktywności',
      size: '120m² + 80m²',
      features: [
        'Sala A: 40 osób, sound system, lustra',
        'Sala B: 20 osób, yoga props, mood lighting',
        'Profesjonalne podłogi taneczne',
        'System nagłośnienia premium',
        'Klimatyzacja w obu salach'
      ],
      equipment: ['Maty do jogi', 'Bloki', 'Pasy', 'Piłki fitness', 'Taśmy oporowe'],
      capacity: 40,
      specialNotes: 'Możliwość rezerwacji na prywatne sesje'
    },
    {
      id: '3',
      name: 'Strefa Wellness',
      description: 'Regeneracja to część treningu - zadbaj o swoje ciało',
      size: '150m²',
      features: [
        'Sauna fińska (max 8 osób)',
        'Strefa stretching z matami',
        'Muscle recovery station',
        'Quiet zone dla medytacji',
        'Strefa relaksu z fotelem masującym'
      ],
      equipment: ['Sauna', 'Maty', 'Roller', 'Piłki do masażu', 'Fotel masujący'],
      capacity: 15,
      specialNotes: 'Dostępna dla członków Premium i VIP'
    },
    {
      id: '4',
      name: 'Udogodnienia',
      description: 'Komfort na każdym kroku - wszystko czego potrzebujesz',
      size: '200m²',
      features: [
        'Szatnie z lockerami (gratis dla członków)',
        'Prysznice z gorącą wodą 24/7',
        'Parking dla 30 aut (gratis)',
        'Reception z healthy snacks',
        'Strefa coworkingu z WiFi'
      ],
      equipment: ['Lockery', 'Suszarki', 'Kosmetyki', 'Ręczniki', 'WiFi'],
      capacity: 100,
      specialNotes: 'Wszystkie udogodnienia dostępne dla każdego członka'
    }
  ];

  const getFilteredClasses = () => {
    return fitnessClasses.filter(fitnessClass => {
      const hasScheduleForDay = fitnessClass.schedule.some(schedule => schedule.dayOfWeek === selectedDay);
      if (!hasScheduleForDay) return false;

      if (selectedFilter === 'Wszystkie zajęcia') return true;
      if (selectedFilter === fitnessClass.category) return true;
      if (selectedFilter === fitnessClass.difficulty) return true;
      return false;
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Początkujący': return 'theme-fitness-green';
      case 'Średnio zaawansowany': return 'theme-fitness-orange';
      case 'Zaawansowany': return 'theme-fitness-red';
      default: return 'theme-text-muted';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Cardio & HIIT': return <Zap className="w-5 h-5" />;
      case 'Siła & CrossFit': return <Dumbbell className="w-5 h-5" />;
      case 'Yoga & Stretching': return <Heart className="w-5 h-5" />;
      case 'Taniec & Zumba': return <Star className="w-5 h-5" />;
      default: return <Target className="w-5 h-5" />;
    }
  };

  const handleBookClass = (fitnessClass: FitnessClass, schedule: FitnessClass['schedule'][0]) => {
    setSelectedClass({ ...fitnessClass, schedule });
    setShowBookingModal(true);
    setBookingStep(1);
  };

  const closeBookingModal = () => {
    setShowBookingModal(false);
    setSelectedClass(null);
    setBookingStep(1);
  };

  const handleSelectMembership = (membership: typeof membershipTiers[0]) => {
    setSelectedMembership(membership);
    setShowMembershipModal(true);
    setMembershipStep(1);
  };

  const closeMembershipModal = () => {
    setShowMembershipModal(false);
    setSelectedMembership(null);
    setMembershipStep(1);
  };

  const handleViewTrainer = (trainer: typeof trainers[0]) => {
    setSelectedTrainer(trainer);
    setShowTrainerModal(true);
  };

  const closeTrainerModal = () => {
    setShowTrainerModal(false);
    setSelectedTrainer(null);
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen theme-bg-primary theme-text-primary animate-pulse">
        <div className="h-16 theme-bg-secondary"></div>
        <div className="h-96 theme-bg-tertiary"></div>
      </div>
    );
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false);
    }
  };

  return (
    <div className="min-h-screen theme-bg-primary theme-text-primary">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 theme-bg-primary/95 backdrop-blur-sm border-b theme-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 theme-bg-fitness-red rounded-lg flex items-center justify-center">
                <Dumbbell className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold theme-text-primary">FlexFit Studio</h1>
                <p className="text-xs theme-text-muted">Fitness & Wellness</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => scrollToSection('hero')}
                className="text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
              >
                Start
              </button>
              <button
                onClick={() => scrollToSection('schedule')}
                className="text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
              >
                Zajęcia
              </button>
              <button
                onClick={() => scrollToSection('membership')}
                className="text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
              >
                Karnet
              </button>
              <button
                onClick={() => scrollToSection('trainers')}
                className="text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
              >
                Trenerzy
              </button>
              <button
                onClick={() => scrollToSection('transformations')}
                className="text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
              >
                Efekty
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
              >
                Kontakt
              </button>
              <ThemeToggle />
              <button className="theme-btn-fitness-primary px-4 py-2 rounded-lg font-medium transition-all hover:scale-105">
                Trial 7 dni
              </button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center space-x-2">
              <ThemeToggle />
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="theme-text-secondary hover:theme-text-primary p-2"
                aria-label="Toggle menu"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t theme-border">
              <div className="flex flex-col space-y-4">
                <button
                  onClick={() => scrollToSection('hero')}
                  className="text-left text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
                >
                  Start
                </button>
                <button
                  onClick={() => scrollToSection('schedule')}
                  className="text-left text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
                >
                  Zajęcia
                </button>
                <button
                  onClick={() => scrollToSection('membership')}
                  className="text-left text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
                >
                  Karnet
                </button>
                <button
                  onClick={() => scrollToSection('trainers')}
                  className="text-left text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
                >
                  Trenerzy
                </button>
                <button
                  onClick={() => scrollToSection('transformations')}
                  className="text-left text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
                >
                  Efekty
                </button>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="text-left text-xl font-bold theme-text-primary hover:theme-fitness-orange transition-colors"
                >
                  Kontakt
                </button>
                <button className="theme-btn-fitness-primary px-4 py-2 rounded-lg font-medium w-full text-center">
                  Trial 7 dni za 19zł
                </button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-16">
        {/* Hero Section */}
        <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
          {/* Background with overlay */}
          <div className="absolute inset-0 theme-bg-secondary">
            <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/30"></div>
            {/* Enhanced fitness background visualization */}
            <div className="w-full h-full flex items-center justify-center theme-text-muted relative">
              {/* Animated background elements */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-gradient-to-br from-red-500 to-orange-500 animate-pulse"></div>
                <div className="absolute top-3/4 right-1/4 w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-green-500 animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 animate-pulse delay-500"></div>
              </div>

              {/* Main fitness icon */}
              <div className="text-center relative z-10">
                <div className="relative">
                  <Dumbbell className="w-32 h-32 mx-auto mb-4 opacity-20 animate-bounce" />
                  <div className="absolute inset-0 w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-red-500/20 to-orange-500/20 animate-ping"></div>
                </div>
                <p className="text-lg opacity-60 font-medium">Nowoczesna siłownia • Profesjonalni trenerzy • Najlepszy sprzęt</p>
              </div>
            </div>
          </div>

          {/* Hero Content */}
          <div className="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            {/* Main headline with animation */}
            <div className="mb-8 animate-fade-in-up">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 leading-tight">
                Przekształć swoje ciało,<br />
                <span className="theme-fitness-orange bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
                  zmień swoje życie
                </span>
              </h1>

              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mb-6 rounded-full"></div>
            </div>

            {/* Subtitle with enhanced styling */}
            <div className="mb-10 animate-fade-in-up delay-300">
              <p className="text-xl md:text-2xl lg:text-3xl mb-6 text-white max-w-4xl mx-auto leading-relaxed">
                Profesjonalny trening • Nowoczesny sprzęt • Społeczność, która motywuje
              </p>

              <div className="inline-flex items-center px-6 py-3 theme-bg-fitness-green rounded-full text-white font-semibold text-lg shadow-lg">
                <Star className="w-5 h-5 mr-2" />
                Pierwsza wizyta gratis!
              </div>
            </div>

            {/* CTA Buttons with enhanced styling */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up delay-500">
              <button className="group theme-btn-fitness-primary px-10 py-4 rounded-xl text-lg font-bold transition-all duration-300 hover:scale-105 hover:shadow-2xl transform hover:-translate-y-1">
                <span className="flex items-center justify-center">
                  Rozpocznij transformację
                  <Dumbbell className="w-5 h-5 ml-2 group-hover:animate-bounce" />
                </span>
              </button>
              <button className="group theme-btn-fitness-secondary px-10 py-4 rounded-xl text-lg font-bold transition-all duration-300 hover:scale-105 hover:shadow-xl transform hover:-translate-y-1">
                <span className="flex items-center justify-center">
                  <Phone className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                  Bezpłatna wizyta
                </span>
              </button>
            </div>

            {/* Enhanced Stats with animations */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center animate-fade-in-up delay-700">
              <div className="group flex flex-col items-center p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                <div className="relative mb-4">
                  <Users className="w-10 h-10 theme-fitness-orange group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 theme-bg-fitness-green rounded-full animate-pulse"></div>
                </div>
                <div className="text-3xl font-bold mb-2">500+</div>
                <div className="text-gray-300 font-medium">Zadowolonych członków</div>
              </div>

              <div className="group flex flex-col items-center p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                <div className="relative mb-4">
                  <Heart className="w-10 h-10 theme-fitness-orange group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 theme-bg-fitness-green rounded-full animate-pulse delay-300"></div>
                </div>
                <div className="text-3xl font-bold mb-2">50+</div>
                <div className="text-gray-300 font-medium">Zajęć tygodniowo</div>
              </div>

              <div className="group flex flex-col items-center p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                <div className="relative mb-4">
                  <Clock className="w-10 h-10 theme-fitness-orange group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 theme-bg-fitness-green rounded-full animate-pulse delay-500"></div>
                </div>
                <div className="text-3xl font-bold mb-2">6:00-23:00</div>
                <div className="text-gray-300 font-medium">Godziny otwarcia</div>
              </div>
            </div>


          </div>
        </section>

        {/* Class Schedule Section */}
        <section id="schedule" className="py-20 theme-bg-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Section Header */}
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold theme-text-primary mb-4">Plan Zajęć</h2>
              <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
                Wybierz zajęcia dopasowane do Twojego poziomu i celów. Każde zajęcia prowadzone są przez doświadczonych trenerów.
              </p>
              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mt-6 rounded-full"></div>
            </div>

            {/* Filters */}
            <div className="mb-8">
              <div className="flex flex-wrap gap-2 justify-center mb-6">
                {filters.map((filter) => (
                  <button
                    key={filter}
                    onClick={() => setSelectedFilter(filter)}
                    className={`px-4 py-2 rounded-full font-medium transition-all duration-200 ${
                      selectedFilter === filter
                        ? 'theme-btn-fitness-primary'
                        : 'theme-bg-secondary theme-text-secondary hover:theme-bg-tertiary'
                    }`}
                  >
                    {filter}
                  </button>
                ))}
              </div>

              {/* Day selector */}
              <div className="flex flex-wrap gap-2 justify-center">
                {days.map((day) => (
                  <button
                    key={day}
                    onClick={() => setSelectedDay(day)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      selectedDay === day
                        ? 'theme-btn-fitness-secondary'
                        : 'theme-text-muted hover:theme-text-secondary'
                    }`}
                  >
                    {day}
                  </button>
                ))}
              </div>
            </div>

            {/* Classes Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {getFilteredClasses().map((fitnessClass) => {
                const todaySchedule = fitnessClass.schedule.find(s => s.dayOfWeek === selectedDay);
                if (!todaySchedule) return null;

                return (
                  <div key={fitnessClass.id} className="theme-bg-card rounded-2xl p-6 border theme-border hover:shadow-lg transition-all duration-300 hover:scale-105">
                    {/* Class header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 theme-bg-secondary rounded-lg">
                          {getCategoryIcon(fitnessClass.category)}
                        </div>
                        <div>
                          <h3 className="font-bold theme-text-primary text-lg">{fitnessClass.name}</h3>
                          <p className="theme-text-muted text-sm">{fitnessClass.trainer}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(fitnessClass.difficulty)}`}>
                        {fitnessClass.difficulty}
                      </span>
                    </div>

                    {/* Time and duration */}
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 theme-text-muted" />
                        <span className="theme-text-secondary font-medium">
                          {todaySchedule.startTime} - {todaySchedule.endTime}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Target className="w-4 h-4 theme-text-muted" />
                        <span className="theme-text-secondary text-sm">{fitnessClass.duration} min</span>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="theme-text-secondary text-sm mb-4 leading-relaxed">
                      {fitnessClass.description}
                    </p>

                    {/* Stats */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center p-3 theme-bg-secondary rounded-lg">
                        <div className="font-bold theme-text-primary">{fitnessClass.caloriesBurned}</div>
                        <div className="text-xs theme-text-muted">Kalorie</div>
                      </div>
                      <div className="text-center p-3 theme-bg-secondary rounded-lg">
                        <div className="font-bold theme-text-primary">
                          {fitnessClass.currentParticipants}/{fitnessClass.maxParticipants}
                        </div>
                        <div className="text-xs theme-text-muted">Miejsca</div>
                      </div>
                    </div>

                    {/* Booking button */}
                    <div className="space-y-2">
                      {todaySchedule.isAvailable ? (
                        <button
                          onClick={() => handleBookClass(fitnessClass, todaySchedule)}
                          className="w-full theme-btn-fitness-primary py-3 rounded-lg font-semibold transition-all hover:scale-105"
                        >
                          Zapisz się
                        </button>
                      ) : (
                        <div className="text-center">
                          <button
                            onClick={() => handleBookClass(fitnessClass, todaySchedule)}
                            className="w-full theme-btn-fitness-secondary py-3 rounded-lg font-semibold"
                          >
                            Dołącz do listy oczekujących
                          </button>
                          {todaySchedule.waitingList && (
                            <p className="text-xs theme-text-muted mt-1">
                              Lista oczekujących: {todaySchedule.waitingList} osób
                            </p>
                          )}
                        </div>
                      )}

                      <div className="flex items-center justify-center space-x-4 text-xs theme-text-muted">
                        <span>Sprzęt: {fitnessClass.equipment.join(', ')}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* No classes message */}
            {getFilteredClasses().length === 0 && (
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 theme-text-muted mx-auto mb-4" />
                <h3 className="text-xl font-semibold theme-text-primary mb-2">Brak zajęć w tym dniu</h3>
                <p className="theme-text-secondary">Wybierz inny dzień lub kategorię zajęć</p>
              </div>
            )}
          </div>
        </section>

        {/* Membership Tiers Section */}
        <section id="membership" className="py-20 theme-bg-secondary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold theme-text-primary mb-4">Pakiety Członkowskie</h2>
              <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
                Wybierz pakiet dopasowany do Twoich potrzeb i celów fitness. Każdy pakiet zapewnia dostęp do profesjonalnego sprzętu i wsparcia trenerów.
              </p>
              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mt-6 rounded-full"></div>
            </div>

            {/* Trial Offer Banner */}
            <div className="mb-12 text-center">
              <div className="inline-flex items-center px-8 py-4 theme-bg-fitness-green rounded-2xl text-white font-bold text-lg shadow-lg">
                <Star className="w-6 h-6 mr-3" />
                7 dni za 19 zł - poznaj naszą filozofię!
                <Star className="w-6 h-6 ml-3" />
              </div>
            </div>

            {/* Membership Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {membershipTiers.map((tier) => {
                const IconComponent = tier.icon;
                return (
                  <div
                    key={tier.id}
                    className={`relative theme-bg-card rounded-3xl p-8 border-2 transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                      tier.isPopular
                        ? 'border-orange-500 shadow-lg transform scale-105'
                        : 'theme-border hover:border-orange-300'
                    }`}
                  >
                    {/* Popular badge */}
                    {tier.isPopular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <div className="theme-bg-fitness-orange text-white px-6 py-2 rounded-full font-bold text-sm shadow-lg">
                          ⭐ NAJPOPULARNIEJSZY
                        </div>
                      </div>
                    )}

                    {/* Header */}
                    <div className="text-center mb-8">
                      <div className="flex justify-center mb-4">
                        <div className={`p-4 rounded-2xl ${
                          tier.id === 'basic' ? 'theme-bg-fitness-blue' :
                          tier.id === 'premium' ? 'theme-bg-fitness-orange' :
                          'theme-bg-fitness-red'
                        }`}>
                          <IconComponent className="w-8 h-8 text-white" />
                        </div>
                      </div>

                      <h3 className="text-2xl font-bold theme-text-primary mb-2">{tier.name}</h3>
                      <p className="theme-text-secondary text-sm mb-4">{tier.description}</p>

                      <div className="mb-4">
                        <span className="text-4xl font-bold theme-text-primary">{tier.price} zł</span>
                        <span className="theme-text-muted">/{tier.period}</span>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="space-y-4 mb-8">
                      {tier.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-start space-x-3">
                          <Check className="w-5 h-5 theme-fitness-green mt-0.5 flex-shrink-0" />
                          <span className="theme-text-secondary text-sm leading-relaxed">{feature}</span>
                        </div>
                      ))}

                      {tier.restrictions.map((restriction, restrictionIndex) => (
                        <div key={restrictionIndex} className="flex items-start space-x-3">
                          <X className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                          <span className="theme-text-muted text-sm leading-relaxed">{restriction}</span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <button
                      onClick={() => handleSelectMembership(tier)}
                      className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 ${
                        tier.isPopular
                          ? 'theme-btn-fitness-primary shadow-lg'
                          : 'theme-btn-fitness-secondary'
                      }`}
                    >
                      Wybierz {tier.name}
                    </button>

                    {/* Additional info */}
                    <div className="mt-4 text-center">
                      <p className="text-xs theme-text-muted">
                        Bez zobowiązań • Anuluj w każdej chwili
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Additional Benefits */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="p-6">
                <div className="w-16 h-16 theme-bg-fitness-blue rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold theme-text-primary mb-2">Bez zobowiązań</h4>
                <p className="theme-text-secondary text-sm">Anuluj członkostwo w każdej chwili bez dodatkowych opłat</p>
              </div>

              <div className="p-6">
                <div className="w-16 h-16 theme-bg-fitness-green rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold theme-text-primary mb-2">Społeczność</h4>
                <p className="theme-text-secondary text-sm">Dołącz do grupy 500+ aktywnych członków, którzy osiągają swoje cele</p>
              </div>

              <div className="p-6">
                <div className="w-16 h-16 theme-bg-fitness-orange rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold theme-text-primary mb-2">Wsparcie 24/7</h4>
                <p className="theme-text-secondary text-sm">Profesjonalni trenerzy zawsze gotowi pomóc w osiągnięciu celów</p>
              </div>
            </div>
          </div>
        </section>

        {/* Trainers Section */}
        <section id="trainers" className="py-20 theme-bg-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold theme-text-primary mb-4">Nasi Trenerzy</h2>
              <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
                Poznaj nasz zespół profesjonalnych trenerów. Każdy z nich ma bogate doświadczenie i pasję do pomagania innym w osiąganiu celów fitness.
              </p>
              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mt-6 rounded-full"></div>
            </div>

            {/* Trainers Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {trainers.map((trainer) => (
                <div key={trainer.id} className="group theme-bg-card rounded-3xl p-8 border theme-border hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  {/* Trainer Image Placeholder */}
                  <div className="relative mb-6">
                    <div className="w-32 h-32 mx-auto rounded-full theme-bg-secondary flex items-center justify-center overflow-hidden">
                      <User className="w-16 h-16 theme-text-muted" />
                    </div>
                    {/* Status indicator */}
                    <div className="absolute bottom-2 right-1/2 transform translate-x-16 w-6 h-6 theme-bg-fitness-green rounded-full border-4 theme-border flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </div>

                  {/* Trainer Info */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold theme-text-primary mb-1">{trainer.name}</h3>
                    <p className="theme-fitness-orange font-semibold mb-3">{trainer.title}</p>
                    <p className="theme-text-secondary text-sm leading-relaxed">{trainer.description}</p>
                  </div>

                  {/* Experience */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center space-x-2 mb-3">
                      <Award className="w-5 h-5 theme-fitness-blue" />
                      <span className="font-semibold theme-text-primary">{trainer.experience}</span>
                    </div>
                  </div>

                  {/* Specializations */}
                  <div className="mb-6">
                    <h4 className="font-semibold theme-text-primary mb-3 text-center">Specjalizacje:</h4>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {trainer.specializations.slice(0, 3).map((spec, specIndex) => (
                        <span key={specIndex} className="px-3 py-1 theme-bg-secondary theme-text-secondary text-xs rounded-full">
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Certifications */}
                  <div className="mb-6">
                    <h4 className="font-semibold theme-text-primary mb-3 text-center">Certyfikaty:</h4>
                    <div className="space-y-2">
                      {trainer.certifications.slice(0, 2).map((cert, certIndex) => (
                        <div key={certIndex} className="flex items-center justify-center space-x-2">
                          <Check className="w-4 h-4 theme-fitness-green" />
                          <span className="theme-text-secondary text-sm">{cert}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Personal Records */}
                  <div className="mb-6">
                    <h4 className="font-semibold theme-text-primary mb-3 text-center">Osiągnięcia:</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {Object.entries(trainer.personalRecords).slice(0, 2).map(([key, value], recordIndex) => (
                        <div key={recordIndex} className="flex justify-between items-center p-2 theme-bg-secondary rounded-lg">
                          <span className="theme-text-secondary text-sm">{key}:</span>
                          <span className="font-bold theme-fitness-red text-sm">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Social Media */}
                  <div className="flex justify-center space-x-4 mb-6">
                    {trainer.socialMedia?.instagram && (
                      <div className="flex items-center space-x-2 theme-bg-secondary px-3 py-2 rounded-lg">
                        <Instagram className="w-4 h-4 theme-fitness-orange" />
                        <span className="theme-text-secondary text-sm">{trainer.socialMedia.instagram}</span>
                      </div>
                    )}
                  </div>

                  {/* CTA Button */}
                  <button
                    onClick={() => handleViewTrainer(trainer)}
                    className="w-full theme-btn-fitness-secondary py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
                  >
                    Zobacz profil
                  </button>
                </div>
              ))}
            </div>

            {/* Team Stats */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div className="p-6">
                <div className="text-3xl font-bold theme-fitness-red mb-2">5</div>
                <div className="theme-text-secondary">Doświadczonych trenerów</div>
              </div>
              <div className="p-6">
                <div className="text-3xl font-bold theme-fitness-orange mb-2">25+</div>
                <div className="theme-text-secondary">Lat łącznego doświadczenia</div>
              </div>
              <div className="p-6">
                <div className="text-3xl font-bold theme-fitness-blue mb-2">15+</div>
                <div className="theme-text-secondary">Międzynarodowych certyfikatów</div>
              </div>
              <div className="p-6">
                <div className="text-3xl font-bold theme-fitness-green mb-2">500+</div>
                <div className="theme-text-secondary">Zadowolonych klientów</div>
              </div>
            </div>
          </div>
        </section>

        {/* Transformation Gallery Section */}
        <section id="transformations" className="py-20 theme-bg-secondary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold theme-text-primary mb-4">Prawdziwe Transformacje</h2>
              <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
                Każda historia to dowód na to, że wszystko jest możliwe. Poznaj inspirujące historie naszych członków.
              </p>
              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mt-6 rounded-full"></div>
            </div>

            {/* Transformation Stories Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {transformationStories.map((story) => (
                <div key={story.id} className="theme-bg-card rounded-3xl p-8 border theme-border hover:shadow-2xl transition-all duration-300">
                  {/* Story Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-2xl font-bold theme-text-primary">{story.memberName}, {story.age} lat</h3>
                      <p className="theme-text-secondary">Transformacja w {story.timeframe}</p>
                    </div>
                    <div className="text-right">
                      <div className="theme-bg-fitness-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                        {story.membershipType}
                      </div>
                    </div>
                  </div>

                  {/* Before/After Comparison */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {/* Before */}
                    <div className="text-center">
                      <div className="theme-bg-secondary rounded-2xl p-6 mb-4">
                        <div className="w-24 h-24 mx-auto theme-bg-tertiary rounded-full flex items-center justify-center mb-4">
                          <User className="w-12 h-12 theme-text-muted" />
                        </div>
                        <h4 className="font-bold theme-text-primary mb-2">PRZED</h4>
                        {story.before.weight !== 'Nie o wagę chodziło' && (
                          <div className="flex items-center justify-center space-x-2 mb-2">
                            <Scale className="w-4 h-4 theme-text-muted" />
                            <span className="font-semibold theme-fitness-red">{story.before.weight}</span>
                          </div>
                        )}
                        <p className="theme-text-secondary text-sm">{story.before.description}</p>
                      </div>
                    </div>

                    {/* After */}
                    <div className="text-center">
                      <div className="theme-bg-fitness-green/10 border-2 border-green-200 rounded-2xl p-6 mb-4">
                        <div className="w-24 h-24 mx-auto theme-bg-fitness-green rounded-full flex items-center justify-center mb-4">
                          <TrendingUp className="w-12 h-12 text-white" />
                        </div>
                        <h4 className="font-bold theme-text-primary mb-2">PO</h4>
                        {story.after.weight !== 'Silna mama' && (
                          <div className="flex items-center justify-center space-x-2 mb-2">
                            <Scale className="w-4 h-4 theme-text-muted" />
                            <span className="font-semibold theme-fitness-green">{story.after.weight}</span>
                          </div>
                        )}
                        <p className="theme-text-secondary text-sm">{story.after.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* Quote */}
                  <div className="mb-6 p-6 theme-bg-secondary rounded-2xl border-l-4 theme-border">
                    <div className="flex items-start space-x-3">
                      <div className="text-4xl theme-fitness-orange leading-none">&ldquo;</div>
                      <p className="theme-text-primary italic text-lg leading-relaxed">{story.quote}</p>
                    </div>
                  </div>

                  {/* Achievements */}
                  <div className="mb-6">
                    <h4 className="font-bold theme-text-primary mb-4 flex items-center">
                      <Award className="w-5 h-5 theme-fitness-orange mr-2" />
                      Kluczowe osiągnięcia:
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {story.achievements.map((achievement, achievementIndex) => (
                        <div key={achievementIndex} className="flex items-center space-x-3 p-3 theme-bg-secondary rounded-lg">
                          <Check className="w-5 h-5 theme-fitness-green flex-shrink-0" />
                          <span className="theme-text-secondary text-sm">{achievement}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="flex items-center justify-center space-x-2 p-4 theme-bg-fitness-blue/10 rounded-xl">
                    <Timer className="w-5 h-5 theme-fitness-blue" />
                    <span className="font-semibold theme-text-primary">Czas transformacji: {story.timeframe}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Call to Action */}
            <div className="mt-16 text-center">
              <div className="theme-bg-card rounded-3xl p-8 border theme-border max-w-4xl mx-auto">
                <h3 className="text-3xl font-bold theme-text-primary mb-4">Twoja historia może być następna!</h3>
                <p className="text-xl theme-text-secondary mb-8">
                  Dołącz do grona osób, które zmieniły swoje życie dzięki FlexFit Studio. Każda wielka podróż zaczyna się od pierwszego kroku.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="theme-btn-fitness-primary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                    Rozpocznij swoją transformację
                  </button>
                  <button className="theme-btn-fitness-secondary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                    Umów bezpłatną konsultację
                  </button>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div className="p-4">
                    <div className="text-2xl font-bold theme-fitness-red mb-2">500+</div>
                    <div className="theme-text-secondary text-sm">Udanych transformacji</div>
                  </div>
                  <div className="p-4">
                    <div className="text-2xl font-bold theme-fitness-orange mb-2">95%</div>
                    <div className="theme-text-secondary text-sm">Osiąga swoje cele</div>
                  </div>
                  <div className="p-4">
                    <div className="text-2xl font-bold theme-fitness-green mb-2">6 mies.</div>
                    <div className="theme-text-secondary text-sm">Średni czas transformacji</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Facility Tour Section */}
        <section id="facility" className="py-20 theme-bg-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold theme-text-primary mb-4">Poznaj Nasze Przestrzenie</h2>
              <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
                Nowoczesny sprzęt, komfortowe warunki, wszystko czego potrzebujesz do osiągnięcia swoich celów fitness.
              </p>
              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mt-6 rounded-full"></div>
            </div>

            {/* Facility Areas Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {facilityAreas.map((area) => (
                <div key={area.id} className="theme-bg-card rounded-3xl p-8 border theme-border hover:shadow-2xl transition-all duration-300">
                  {/* Area Header */}
                  <div className="flex items-start justify-between mb-6">
                    <div>
                      <h3 className="text-2xl font-bold theme-text-primary mb-2">{area.name}</h3>
                      <p className="theme-text-secondary">{area.description}</p>
                    </div>
                    <div className="text-right">
                      <div className="theme-bg-fitness-blue text-white px-3 py-1 rounded-full text-sm font-semibold">
                        {area.size}
                      </div>
                    </div>
                  </div>

                  {/* Area Image Placeholder */}
                  <div className="mb-6 h-48 theme-bg-secondary rounded-2xl flex items-center justify-center">
                    <div className="text-center">
                      <Home className="w-16 h-16 theme-text-muted mx-auto mb-2" />
                      <p className="theme-text-muted text-sm">Zdjęcie: {area.name}</p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="font-bold theme-text-primary mb-4 flex items-center">
                      <Star className="w-5 h-5 theme-fitness-orange mr-2" />
                      Główne cechy:
                    </h4>
                    <div className="space-y-3">
                      {area.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-start space-x-3">
                          <Check className="w-5 h-5 theme-fitness-green mt-0.5 flex-shrink-0" />
                          <span className="theme-text-secondary text-sm leading-relaxed">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Equipment */}
                  <div className="mb-6">
                    <h4 className="font-bold theme-text-primary mb-3">Dostępny sprzęt:</h4>
                    <div className="flex flex-wrap gap-2">
                      {area.equipment.map((item, itemIndex) => (
                        <span key={itemIndex} className="px-3 py-1 theme-bg-secondary theme-text-secondary text-sm rounded-full">
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Capacity and Special Notes */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 theme-bg-secondary rounded-xl">
                      <div className="flex items-center space-x-2">
                        <Users className="w-5 h-5 theme-text-muted" />
                        <span className="theme-text-secondary">Pojemność:</span>
                      </div>
                      <span className="font-bold theme-text-primary">{area.capacity} osób</span>
                    </div>

                    {area.specialNotes && (
                      <div className="p-4 theme-bg-fitness-blue/10 border border-blue-200 rounded-xl">
                        <div className="flex items-start space-x-2">
                          <Star className="w-5 h-5 theme-fitness-blue mt-0.5 flex-shrink-0" />
                          <p className="theme-text-secondary text-sm">{area.specialNotes}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Additional Amenities */}
            <div className="mt-16">
              <h3 className="text-3xl font-bold theme-text-primary text-center mb-12">Dodatkowe Udogodnienia</h3>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div className="text-center p-6 theme-bg-card rounded-2xl border theme-border">
                  <div className="w-16 h-16 theme-bg-fitness-green rounded-full flex items-center justify-center mx-auto mb-4">
                    <Car className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="font-bold theme-text-primary mb-2">Parking Gratis</h4>
                  <p className="theme-text-secondary text-sm">30 miejsc parkingowych dla członków</p>
                </div>

                <div className="text-center p-6 theme-bg-card rounded-2xl border theme-border">
                  <div className="w-16 h-16 theme-bg-fitness-blue rounded-full flex items-center justify-center mx-auto mb-4">
                    <Wifi className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="font-bold theme-text-primary mb-2">WiFi Premium</h4>
                  <p className="theme-text-secondary text-sm">Szybki internet w całym obiekcie</p>
                </div>

                <div className="text-center p-6 theme-bg-card rounded-2xl border theme-border">
                  <div className="w-16 h-16 theme-bg-fitness-orange rounded-full flex items-center justify-center mx-auto mb-4">
                    <Coffee className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="font-bold theme-text-primary mb-2">Healthy Bar</h4>
                  <p className="theme-text-secondary text-sm">Zdrowe przekąski i napoje</p>
                </div>

                <div className="text-center p-6 theme-bg-card rounded-2xl border theme-border">
                  <div className="w-16 h-16 theme-bg-fitness-red rounded-full flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="font-bold theme-text-primary mb-2">Strefa Relaksu</h4>
                  <p className="theme-text-secondary text-sm">Miejsce na odpoczynek po treningu</p>
                </div>
              </div>
            </div>

            {/* Virtual Tour CTA */}
            <div className="mt-16 text-center">
              <div className="theme-bg-card rounded-3xl p-8 border theme-border max-w-4xl mx-auto">
                <h3 className="text-3xl font-bold theme-text-primary mb-4">Chcesz zobaczyć na własne oczy?</h3>
                <p className="text-xl theme-text-secondary mb-8">
                  Umów się na bezpłatną wizytę i zobacz nasze przestrzenie na żywo. Nasi trenerzy z przyjemnością oprowadzą Cię po całym obiekcie.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="theme-btn-fitness-primary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                    Umów wizytę
                  </button>
                  <button className="theme-btn-fitness-secondary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                    Wirtualny spacer
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact & Location Section */}
        <section id="contact" className="py-20 theme-bg-secondary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold theme-text-primary mb-4">Kontakt & Lokalizacja</h2>
              <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
                Masz pytania? Chcesz umówić wizytę? Skontaktuj się z nami - jesteśmy tutaj, aby Ci pomóc!
              </p>
              <div className="w-24 h-1 theme-bg-fitness-red mx-auto mt-6 rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="theme-bg-card rounded-3xl p-8 border theme-border">
                <h3 className="text-2xl font-bold theme-text-primary mb-6">Umów wizytę lub zadaj pytanie</h3>

                <form className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Imię i nazwisko *</label>
                      <input
                        type="text"
                        className="w-full p-3 theme-bg-secondary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        placeholder="Jan Kowalski"
                      />
                    </div>
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Telefon *</label>
                      <input
                        type="tel"
                        className="w-full p-3 theme-bg-secondary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        placeholder="+48 123 456 789"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Email</label>
                    <input
                      type="email"
                      className="w-full p-3 theme-bg-secondary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Interesujesz się</label>
                      <select className="w-full p-3 theme-bg-secondary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        <option>Membership</option>
                        <option>Personal Training</option>
                        <option>Zajęcia grupowe</option>
                        <option>Współpraca</option>
                      </select>
                    </div>
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Twój poziom fitness</label>
                      <select className="w-full p-3 theme-bg-secondary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        <option>Początkujący</option>
                        <option>Średnio zaawansowany</option>
                        <option>Zaawansowany</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Wiadomość</label>
                    <textarea
                      rows={4}
                      className="w-full p-3 theme-bg-secondary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Opisz swoje cele fitness lub zadaj pytanie..."
                    ></textarea>
                  </div>

                  <button className="w-full theme-btn-fitness-primary py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                    Wyślij wiadomość
                  </button>
                </form>
              </div>

              {/* Contact Info & Location */}
              <div className="space-y-8">
                {/* Studio Details */}
                <div className="theme-bg-card rounded-3xl p-8 border theme-border">
                  <h3 className="text-2xl font-bold theme-text-primary mb-6">FlexFit Studio</h3>

                  <div className="space-y-4">
                    <div className="flex items-start space-x-4">
                      <MapPin className="w-6 h-6 theme-fitness-red mt-1 flex-shrink-0" />
                      <div>
                        <p className="font-semibold theme-text-primary">Adres</p>
                        <p className="theme-text-secondary">ul. Sportowa 8, 02-844 Warszawa</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <Phone className="w-6 h-6 theme-fitness-red mt-1 flex-shrink-0" />
                      <div>
                        <p className="font-semibold theme-text-primary">Telefon</p>
                        <p className="theme-text-secondary">+48 555 456 789</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <User className="w-6 h-6 theme-fitness-red mt-1 flex-shrink-0" />
                      <div>
                        <p className="font-semibold theme-text-primary">Email</p>
                        <p className="theme-text-secondary"><EMAIL></p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <Phone className="w-6 h-6 theme-fitness-red mt-1 flex-shrink-0" />
                      <div>
                        <p className="font-semibold theme-text-primary">WhatsApp</p>
                        <p className="theme-text-secondary">+48 555 456 789</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Opening Hours */}
                <div className="theme-bg-card rounded-3xl p-8 border theme-border">
                  <h3 className="text-2xl font-bold theme-text-primary mb-6 flex items-center">
                    <Clock className="w-6 h-6 theme-fitness-orange mr-3" />
                    Godziny Otwarcia
                  </h3>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 theme-bg-secondary rounded-lg">
                      <span className="theme-text-primary font-medium">Poniedziałek - Piątek</span>
                      <span className="theme-fitness-green font-bold">6:00 - 23:00</span>
                    </div>
                    <div className="flex justify-between items-center p-3 theme-bg-secondary rounded-lg">
                      <span className="theme-text-primary font-medium">Sobota</span>
                      <span className="theme-fitness-green font-bold">7:00 - 22:00</span>
                    </div>
                    <div className="flex justify-between items-center p-3 theme-bg-secondary rounded-lg">
                      <span className="theme-text-primary font-medium">Niedziela</span>
                      <span className="theme-fitness-green font-bold">8:00 - 21:00</span>
                    </div>
                  </div>

                  <div className="mt-4 p-4 theme-bg-fitness-blue/10 border border-blue-200 rounded-xl">
                    <p className="theme-text-secondary text-sm">
                      <strong>Reception:</strong> Pn-Pt 9:00-21:00, Weekend 10:00-18:00
                    </p>
                  </div>
                </div>

                {/* Transportation */}
                <div className="theme-bg-card rounded-3xl p-8 border theme-border">
                  <h3 className="text-2xl font-bold theme-text-primary mb-6">Dojazd</h3>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 theme-bg-fitness-blue rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-sm">M</span>
                      </div>
                      <span className="theme-text-secondary">2 minuty od stacji Metro Imielin</span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 theme-bg-fitness-green rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-sm">🚌</span>
                      </div>
                      <span className="theme-text-secondary">Autobus 164, 199 - przystanek Sportowa</span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Car className="w-8 h-8 theme-fitness-orange" />
                      <span className="theme-text-secondary">Parking dla członków zawsze gratis</span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 theme-bg-fitness-red rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-sm">🚲</span>
                      </div>
                      <span className="theme-text-secondary">Bike rack + shower facilities</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Map Placeholder */}
            <div className="mt-12">
              <div className="theme-bg-card rounded-3xl p-8 border theme-border">
                <h3 className="text-2xl font-bold theme-text-primary mb-6 text-center">Lokalizacja na mapie</h3>
                <div className="h-64 theme-bg-secondary rounded-2xl flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-16 h-16 theme-text-muted mx-auto mb-4" />
                    <p className="theme-text-muted">Mapa Google z lokalizacją FlexFit Studio</p>
                    <p className="theme-text-muted text-sm mt-2">ul. Sportowa 8, 02-844 Warszawa</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-12 text-center">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="theme-btn-fitness-primary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                  Umów wizytę
                </button>
                <button className="theme-btn-fitness-secondary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                  Trial 7 dni
                </button>
                <button className="theme-btn-fitness-secondary px-8 py-4 rounded-xl text-lg font-bold transition-all hover:scale-105">
                  Zadzwoń teraz
                </button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Booking Modal */}
      {showBookingModal && selectedClass && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={closeBookingModal}
        >
          <div
            className="theme-bg-card rounded-3xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto border theme-border"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold theme-text-primary">Rezerwacja zajęć</h2>
              <button
                onClick={closeBookingModal}
                className="p-2 hover:theme-bg-secondary rounded-lg transition-colors"
              >
                <X className="w-6 h-6 theme-text-muted" />
              </button>
            </div>

            {/* Class Info */}
            <div className="mb-8 p-6 theme-bg-secondary rounded-2xl">
              <div className="flex items-center space-x-4 mb-4">
                {getCategoryIcon(selectedClass.category)}
                <div>
                  <h3 className="text-xl font-bold theme-text-primary">{selectedClass.name}</h3>
                  <p className="theme-text-secondary">{selectedClass.trainer}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="theme-text-muted">Data:</span>
                  <span className="theme-text-primary font-semibold ml-2">{selectedDay}</span>
                </div>
                <div>
                  <span className="theme-text-muted">Godzina:</span>
                  <span className="theme-text-primary font-semibold ml-2">
                    {selectedClass.schedule.startTime} - {selectedClass.schedule.endTime}
                  </span>
                </div>
                <div>
                  <span className="theme-text-muted">Czas trwania:</span>
                  <span className="theme-text-primary font-semibold ml-2">{selectedClass.duration} min</span>
                </div>
                <div>
                  <span className="theme-text-muted">Dostępne miejsca:</span>
                  <span className="theme-fitness-green font-semibold ml-2">
                    {selectedClass.maxParticipants - selectedClass.currentParticipants}/{selectedClass.maxParticipants}
                  </span>
                </div>
              </div>
            </div>

            {/* Booking Steps */}
            {bookingStep === 1 && (
              <div>
                <h3 className="text-lg font-bold theme-text-primary mb-4">Krok 1: Dane osobowe</h3>
                <form className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Imię i nazwisko *</label>
                      <input
                        type="text"
                        className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                        placeholder="Jan Kowalski"
                      />
                    </div>
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Telefon *</label>
                      <input
                        type="tel"
                        className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                        placeholder="+48 123 456 789"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Status członkostwa</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <label className="flex items-center p-4 theme-bg-primary border theme-border rounded-lg cursor-pointer hover:border-orange-500">
                        <input type="radio" name="membership" value="member" className="mr-3" />
                        <div>
                          <div className="font-semibold theme-text-primary">Jestem członkiem</div>
                          <div className="text-sm theme-text-muted">Mam aktywny karnet</div>
                        </div>
                      </label>
                      <label className="flex items-center p-4 theme-bg-primary border theme-border rounded-lg cursor-pointer hover:border-orange-500">
                        <input type="radio" name="membership" value="guest" className="mr-3" defaultChecked />
                        <div>
                          <div className="font-semibold theme-text-primary">Jestem gościem</div>
                          <div className="text-sm theme-text-muted">Płatność jednorazowa</div>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Specjalne potrzeby (opcjonalne)</label>
                    <textarea
                      rows={3}
                      className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                      placeholder="Kontuzje, ograniczenia, preferencje..."
                    ></textarea>
                  </div>

                  <div className="flex gap-4">
                    <button
                      type="button"
                      onClick={closeBookingModal}
                      className="flex-1 theme-btn-fitness-secondary py-3 rounded-lg font-semibold"
                    >
                      Anuluj
                    </button>
                    <button
                      type="button"
                      onClick={() => setBookingStep(2)}
                      className="flex-1 theme-btn-fitness-primary py-3 rounded-lg font-semibold"
                    >
                      Dalej
                    </button>
                  </div>
                </form>
              </div>
            )}

            {bookingStep === 2 && (
              <div>
                <h3 className="text-lg font-bold theme-text-primary mb-4">Krok 2: Potwierdzenie</h3>

                <div className="space-y-6">
                  {/* Booking Summary */}
                  <div className="p-6 theme-bg-secondary rounded-2xl">
                    <h4 className="font-bold theme-text-primary mb-4">Podsumowanie rezerwacji:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Zajęcia:</span>
                        <span className="theme-text-primary font-semibold">{selectedClass.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Trener:</span>
                        <span className="theme-text-primary">{selectedClass.trainer}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Data i godzina:</span>
                        <span className="theme-text-primary">{selectedDay}, {selectedClass.schedule.startTime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Czas trwania:</span>
                        <span className="theme-text-primary">{selectedClass.duration} minut</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Koszt:</span>
                        <span className="theme-fitness-green font-bold">Wliczony w karnet</span>
                      </div>
                    </div>
                  </div>

                  {/* Confirmation Details */}
                  <div className="p-6 theme-bg-fitness-green/10 border border-green-200 rounded-2xl">
                    <h4 className="font-bold theme-text-primary mb-3 flex items-center">
                      <Check className="w-5 h-5 theme-fitness-green mr-2" />
                      Co otrzymasz:
                    </h4>
                    <ul className="space-y-2 text-sm theme-text-secondary">
                      <li>✅ Potwierdzenie SMS na podany numer</li>
                      <li>✅ Przypomnienie 2 godziny przed zajęciami</li>
                      <li>✅ Możliwość anulowania do 2 godzin przed</li>
                      <li>✅ Instrukcje dotyczące sprzętu i przygotowania</li>
                    </ul>
                  </div>

                  {/* Terms */}
                  <div className="text-xs theme-text-muted">
                    <label className="flex items-start space-x-2">
                      <input type="checkbox" className="mt-1" />
                      <span>
                        Akceptuję regulamin siłowni i wyrażam zgodę na przetwarzanie danych osobowych w celu realizacji rezerwacji.
                      </span>
                    </label>
                  </div>

                  <div className="flex gap-4">
                    <button
                      type="button"
                      onClick={() => setBookingStep(1)}
                      className="flex-1 theme-btn-fitness-secondary py-3 rounded-lg font-semibold"
                    >
                      Wstecz
                    </button>
                    <button
                      type="button"
                      onClick={() => setBookingStep(3)}
                      className="flex-1 theme-btn-fitness-primary py-3 rounded-lg font-semibold"
                    >
                      Potwierdź rezerwację
                    </button>
                  </div>
                </div>
              </div>
            )}

            {bookingStep === 3 && (
              <div className="text-center">
                <div className="w-20 h-20 theme-bg-fitness-green rounded-full flex items-center justify-center mx-auto mb-6">
                  <Check className="w-10 h-10 text-white" />
                </div>

                <h3 className="text-2xl font-bold theme-text-primary mb-4">Rezerwacja potwierdzona!</h3>

                <div className="p-6 theme-bg-secondary rounded-2xl mb-6">
                  <p className="theme-text-secondary mb-4">
                    Twoje miejsce na zajęciach <strong>{selectedClass.name}</strong> zostało zarezerwowane.
                  </p>

                  <div className="text-sm space-y-2">
                    <div><strong>Data:</strong> {selectedDay}</div>
                    <div><strong>Godzina:</strong> {selectedClass.schedule.startTime} - {selectedClass.schedule.endTime}</div>
                    <div><strong>Trener:</strong> {selectedClass.trainer}</div>
                    <div><strong>Numer rezerwacji:</strong> #FF{Math.floor(Math.random() * 10000)}</div>
                  </div>
                </div>

                <div className="p-4 theme-bg-fitness-blue/10 border border-blue-200 rounded-xl mb-6">
                  <p className="text-sm theme-text-secondary">
                    📱 SMS z potwierdzeniem zostanie wysłany na Twój telefon<br />
                    ⏰ Przypomnienie otrzymasz 2 godziny przed zajęciami<br />
                    💪 Przynieś ręcznik i butelkę wody!
                  </p>
                </div>

                <button
                  onClick={closeBookingModal}
                  className="w-full theme-btn-fitness-primary py-3 rounded-lg font-semibold"
                >
                  Zamknij
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Membership Signup Modal */}
      {showMembershipModal && selectedMembership && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={closeMembershipModal}
        >
          <div
            className="theme-bg-card rounded-3xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto border theme-border"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold theme-text-primary">Dołącz do FlexFit Studio</h2>
              <button
                onClick={closeMembershipModal}
                className="p-2 hover:theme-bg-secondary rounded-lg transition-colors"
              >
                <X className="w-6 h-6 theme-text-muted" />
              </button>
            </div>

            {/* Selected Plan Info */}
            <div className="mb-8 p-6 theme-bg-secondary rounded-2xl">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-3 rounded-xl ${
                    selectedMembership.id === 'basic' ? 'theme-bg-fitness-blue' :
                    selectedMembership.id === 'premium' ? 'theme-bg-fitness-orange' :
                    'theme-bg-fitness-red'
                  }`}>
                    <selectedMembership.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold theme-text-primary">{selectedMembership.name}</h3>
                    <p className="theme-text-secondary">{selectedMembership.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold theme-text-primary">{selectedMembership.price} zł</div>
                  <div className="theme-text-muted">/{selectedMembership.period}</div>
                </div>
              </div>

              {selectedMembership.isPopular && (
                <div className="inline-flex items-center px-3 py-1 theme-bg-fitness-orange text-white rounded-full text-sm font-semibold">
                  ⭐ Najpopularniejszy wybór
                </div>
              )}
            </div>

            {/* Signup Steps */}
            {membershipStep === 1 && (
              <div>
                <h3 className="text-lg font-bold theme-text-primary mb-4">Krok 1: Dane osobowe</h3>
                <form className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Imię *</label>
                      <input
                        type="text"
                        className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                        placeholder="Jan"
                      />
                    </div>
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Nazwisko *</label>
                      <input
                        type="text"
                        className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                        placeholder="Kowalski"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Email *</label>
                      <input
                        type="email"
                        className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block theme-text-primary font-medium mb-2">Telefon *</label>
                      <input
                        type="tel"
                        className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                        placeholder="+48 123 456 789"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Data urodzenia *</label>
                    <input
                      type="date"
                      className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                    />
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Twój poziom fitness</label>
                    <select className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500">
                      <option>Początkujący</option>
                      <option>Średnio zaawansowany</option>
                      <option>Zaawansowany</option>
                    </select>
                  </div>

                  <div>
                    <label className="block theme-text-primary font-medium mb-2">Cele fitness (opcjonalne)</label>
                    <textarea
                      rows={3}
                      className="w-full p-3 theme-bg-primary theme-text-primary border theme-border rounded-lg focus:ring-2 focus:ring-orange-500"
                      placeholder="Schudnąć, nabrać masy, poprawić kondycję..."
                    ></textarea>
                  </div>

                  <div className="flex gap-4">
                    <button
                      type="button"
                      onClick={closeMembershipModal}
                      className="flex-1 theme-btn-fitness-secondary py-3 rounded-lg font-semibold"
                    >
                      Anuluj
                    </button>
                    <button
                      type="button"
                      onClick={() => setMembershipStep(2)}
                      className="flex-1 theme-btn-fitness-primary py-3 rounded-lg font-semibold"
                    >
                      Dalej
                    </button>
                  </div>
                </form>
              </div>
            )}

            {membershipStep === 2 && (
              <div>
                <h3 className="text-lg font-bold theme-text-primary mb-4">Krok 2: Płatność i potwierdzenie</h3>

                <div className="space-y-6">
                  {/* Trial Offer */}
                  <div className="p-6 theme-bg-fitness-green/10 border border-green-200 rounded-2xl">
                    <h4 className="font-bold theme-text-primary mb-3 flex items-center">
                      <Star className="w-5 h-5 theme-fitness-green mr-2" />
                      Specjalna oferta dla nowych członków!
                    </h4>
                    <p className="theme-text-secondary mb-4">
                      Zacznij od 7-dniowego trialu za tylko 19 zł, a następnie przejdź na wybrany pakiet.
                    </p>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="trial" className="rounded" defaultChecked />
                      <label htmlFor="trial" className="theme-text-primary font-medium">
                        Tak, chcę zacząć od trialu (19 zł za 7 dni)
                      </label>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div>
                    <h4 className="font-bold theme-text-primary mb-4">Metoda płatności</h4>
                    <div className="space-y-3">
                      <label className="flex items-center p-4 theme-bg-primary border theme-border rounded-lg cursor-pointer hover:border-orange-500">
                        <input type="radio" name="payment" value="card" className="mr-3" defaultChecked />
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 theme-bg-fitness-blue rounded flex items-center justify-center">
                            <span className="text-white text-xs font-bold">💳</span>
                          </div>
                          <div>
                            <div className="font-semibold theme-text-primary">Karta płatnicza</div>
                            <div className="text-sm theme-text-muted">Visa, Mastercard, BLIK</div>
                          </div>
                        </div>
                      </label>
                      <label className="flex items-center p-4 theme-bg-primary border theme-border rounded-lg cursor-pointer hover:border-orange-500">
                        <input type="radio" name="payment" value="transfer" className="mr-3" />
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 theme-bg-fitness-green rounded flex items-center justify-center">
                            <span className="text-white text-xs font-bold">🏦</span>
                          </div>
                          <div>
                            <div className="font-semibold theme-text-primary">Przelew bankowy</div>
                            <div className="text-sm theme-text-muted">Tradycyjny przelew</div>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Summary */}
                  <div className="p-6 theme-bg-secondary rounded-2xl">
                    <h4 className="font-bold theme-text-primary mb-4">Podsumowanie:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Trial (7 dni):</span>
                        <span className="theme-text-primary font-semibold">19 zł</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="theme-text-muted">Następnie {selectedMembership.name}:</span>
                        <span className="theme-text-primary font-semibold">{selectedMembership.price} zł/miesiąc</span>
                      </div>
                      <div className="border-t theme-border pt-2 mt-2">
                        <div className="flex justify-between">
                          <span className="theme-text-primary font-bold">Do zapłaty dziś:</span>
                          <span className="theme-fitness-green font-bold text-lg">19 zł</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Terms */}
                  <div className="text-xs theme-text-muted space-y-2">
                    <label className="flex items-start space-x-2">
                      <input type="checkbox" className="mt-1" />
                      <span>
                        Akceptuję regulamin siłowni i warunki członkostwa.
                      </span>
                    </label>
                    <label className="flex items-start space-x-2">
                      <input type="checkbox" className="mt-1" />
                      <span>
                        Wyrażam zgodę na przetwarzanie danych osobowych zgodnie z polityką prywatności.
                      </span>
                    </label>
                  </div>

                  <div className="flex gap-4">
                    <button
                      type="button"
                      onClick={() => setMembershipStep(1)}
                      className="flex-1 theme-btn-fitness-secondary py-3 rounded-lg font-semibold"
                    >
                      Wstecz
                    </button>
                    <button
                      type="button"
                      onClick={() => setMembershipStep(3)}
                      className="flex-1 theme-btn-fitness-primary py-3 rounded-lg font-semibold"
                    >
                      Zapłać i dołącz
                    </button>
                  </div>
                </div>
              </div>
            )}

            {membershipStep === 3 && (
              <div className="text-center">
                <div className="w-20 h-20 theme-bg-fitness-green rounded-full flex items-center justify-center mx-auto mb-6">
                  <Check className="w-10 h-10 text-white" />
                </div>

                <h3 className="text-2xl font-bold theme-text-primary mb-4">Witaj w FlexFit Studio!</h3>

                <div className="p-6 theme-bg-secondary rounded-2xl mb-6">
                  <p className="theme-text-secondary mb-4">
                    Gratulacje! Twoje członkostwo <strong>{selectedMembership.name}</strong> zostało aktywowane.
                  </p>

                  <div className="text-sm space-y-2">
                    <div><strong>Numer członkowski:</strong> FF{Math.floor(Math.random() * 100000)}</div>
                    <div><strong>Pakiet:</strong> {selectedMembership.name}</div>
                    <div><strong>Trial kończy się:</strong> {new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('pl-PL')}</div>
                    <div><strong>Następna płatność:</strong> {selectedMembership.price} zł ({new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('pl-PL')})</div>
                  </div>
                </div>

                <div className="p-4 theme-bg-fitness-blue/10 border border-blue-200 rounded-xl mb-6">
                  <p className="text-sm theme-text-secondary">
                    📧 Szczegóły członkostwa zostały wysłane na Twój email<br />
                    🎯 Umów się na bezpłatną konsultację z trenerem<br />
                    💪 Możesz zacząć treningi już dziś!
                  </p>
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={closeMembershipModal}
                    className="flex-1 theme-btn-fitness-secondary py-3 rounded-lg font-semibold"
                  >
                    Zamknij
                  </button>
                  <button
                    onClick={() => {
                      closeMembershipModal();
                      scrollToSection('schedule');
                    }}
                    className="flex-1 theme-btn-fitness-primary py-3 rounded-lg font-semibold"
                  >
                    Zapisz się na zajęcia
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Trainer Profile Modal */}
      {showTrainerModal && selectedTrainer && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={closeTrainerModal}
        >
          <div
            className="theme-bg-card rounded-3xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto border theme-border"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold theme-text-primary">Profil Trenera</h2>
              <button
                onClick={closeTrainerModal}
                className="p-2 hover:theme-bg-secondary rounded-lg transition-colors"
              >
                <X className="w-6 h-6 theme-text-muted" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Trainer Photo and Basic Info */}
              <div className="lg:col-span-1">
                <div className="text-center mb-6">
                  <div className="w-48 h-48 mx-auto rounded-full theme-bg-secondary flex items-center justify-center overflow-hidden mb-4">
                    <User className="w-24 h-24 theme-text-muted" />
                  </div>
                  <h3 className="text-2xl font-bold theme-text-primary mb-2">{selectedTrainer.name}</h3>
                  <p className="theme-fitness-orange font-semibold text-lg mb-4">{selectedTrainer.title}</p>

                  {/* Status */}
                  <div className="inline-flex items-center px-4 py-2 theme-bg-fitness-green text-white rounded-full text-sm font-semibold mb-4">
                    <div className="w-2 h-2 bg-white rounded-full mr-2"></div>
                    Dostępny dziś
                  </div>

                  {/* Social Media */}
                  {selectedTrainer.socialMedia?.instagram && (
                    <div className="flex justify-center space-x-4">
                      <div className="flex items-center space-x-2 theme-bg-secondary px-4 py-2 rounded-lg">
                        <Instagram className="w-5 h-5 theme-fitness-orange" />
                        <span className="theme-text-secondary">{selectedTrainer.socialMedia.instagram}</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Quick Stats */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 theme-bg-secondary rounded-lg">
                    <span className="theme-text-secondary">Doświadczenie:</span>
                    <span className="font-bold theme-text-primary">{selectedTrainer.experience.split(' ')[0]} lat</span>
                  </div>
                  <div className="flex items-center justify-between p-3 theme-bg-secondary rounded-lg">
                    <span className="theme-text-secondary">Certyfikaty:</span>
                    <span className="font-bold theme-text-primary">{selectedTrainer.certifications.length}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 theme-bg-secondary rounded-lg">
                    <span className="theme-text-secondary">Specjalizacje:</span>
                    <span className="font-bold theme-text-primary">{selectedTrainer.specializations.length}</span>
                  </div>
                </div>
              </div>

              {/* Detailed Information */}
              <div className="lg:col-span-2 space-y-6">
                {/* About */}
                <div>
                  <h4 className="text-xl font-bold theme-text-primary mb-4 flex items-center">
                    <User className="w-6 h-6 theme-fitness-blue mr-2" />
                    O trenerze
                  </h4>
                  <p className="theme-text-secondary leading-relaxed text-lg">{selectedTrainer.description}</p>
                  <p className="theme-text-muted mt-3">{selectedTrainer.experience}</p>
                </div>

                {/* Specializations */}
                <div>
                  <h4 className="text-xl font-bold theme-text-primary mb-4 flex items-center">
                    <Target className="w-6 h-6 theme-fitness-orange mr-2" />
                    Specjalizacje
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {selectedTrainer.specializations.map((spec: string, index: number) => (
                      <div key={index} className="flex items-center space-x-3 p-3 theme-bg-secondary rounded-lg">
                        <Check className="w-5 h-5 theme-fitness-green" />
                        <span className="theme-text-primary font-medium">{spec}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Certifications */}
                <div>
                  <h4 className="text-xl font-bold theme-text-primary mb-4 flex items-center">
                    <Award className="w-6 h-6 theme-fitness-red mr-2" />
                    Certyfikaty i kwalifikacje
                  </h4>
                  <div className="space-y-3">
                    {selectedTrainer.certifications.map((cert: string, index: number) => (
                      <div key={index} className="flex items-center space-x-3 p-4 theme-bg-secondary rounded-lg">
                        <div className="w-10 h-10 theme-bg-fitness-blue rounded-full flex items-center justify-center">
                          <Award className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold theme-text-primary">{cert}</div>
                          <div className="text-sm theme-text-muted">Certyfikat międzynarodowy</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Achievements */}
                <div>
                  <h4 className="text-xl font-bold theme-text-primary mb-4 flex items-center">
                    <Star className="w-6 h-6 theme-fitness-green mr-2" />
                    Osiągnięcia
                  </h4>
                  <div className="space-y-3">
                    {selectedTrainer.achievements.map((achievement: string, index: number) => (
                      <div key={index} className="flex items-center space-x-3 p-3 theme-bg-secondary rounded-lg">
                        <Star className="w-5 h-5 theme-fitness-orange" />
                        <span className="theme-text-primary">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Personal Records */}
                <div>
                  <h4 className="text-xl font-bold theme-text-primary mb-4 flex items-center">
                    <Dumbbell className="w-6 h-6 theme-fitness-red mr-2" />
                    Rekordy osobiste
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {Object.entries(selectedTrainer.personalRecords).map(([exercise, record], index) => (
                      <div key={index} className="text-center p-4 theme-bg-secondary rounded-lg">
                        <div className="text-2xl font-bold theme-fitness-red mb-1">{record}</div>
                        <div className="theme-text-primary font-medium">{exercise}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <button
                onClick={() => {
                  closeTrainerModal();
                  scrollToSection('schedule');
                }}
                className="flex-1 theme-btn-fitness-primary py-4 rounded-xl text-lg font-bold transition-all hover:scale-105"
              >
                Zobacz zajęcia trenera
              </button>
              <button
                onClick={() => {
                  closeTrainerModal();
                  scrollToSection('contact');
                }}
                className="flex-1 theme-btn-fitness-secondary py-4 rounded-xl text-lg font-bold transition-all hover:scale-105"
              >
                Umów trening personalny
              </button>
              <button
                onClick={closeTrainerModal}
                className="sm:w-auto px-8 py-4 theme-bg-secondary theme-text-secondary rounded-xl font-semibold hover:theme-bg-tertiary transition-colors"
              >
                Zamknij
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Demo Disclaimer */}
      <div className="theme-bg-tertiary border-t theme-border py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="theme-text-muted text-sm">
            🎯 <strong>Demo Project</strong> - To jest projekt demonstracyjny stworzony przez{' '}
            <a href="https://qualixsoftware.com" className="theme-fitness-blue hover:underline">
              Qualix Software
            </a>{' '}
            jako przykład profesjonalnej strony dla branży fitness.
          </p>
        </div>
      </div>
    </div>
  );
}
