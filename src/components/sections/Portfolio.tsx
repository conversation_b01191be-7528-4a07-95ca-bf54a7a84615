'use client';

import { useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { EmblaCarouselType } from 'embla-carousel';
import Autoplay from 'embla-carousel-autoplay';
import { Coffee, Scissors, Car, Scale, Dumbbell, ArrowRight, ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react';
import { scrollToElement } from '@/lib/utils/scroll';
import Link from 'next/link';

const demoProjects = [
  {
    icon: Coffee,
    title: 'Kawiarnia Brew & Bean',
    description: 'Nowoczesna strona dla kawiarni z systemem zamówień online',
    features: ['Menu interaktywne', 'Rezerwacje stolików', 'Integracja social media'],
    color: 'from-amber-500 to-amber-600',
    bgColor: 'bg-amber-100 dark:bg-amber-900/30',
    demoUrl: '/demo/brew-and-bean',
    industry: 'Gastronomia',
    tech: ['Next.js', 'Tailwind CSS', 'TypeScript']
  },
  {
    icon: Scissors,
    title: 'Salon Fryzjerski Style Studio',
    description: 'Luksusowy salon fryzjerski z integracją Booksy',
    features: ['Galeria portfolio', 'Rezerwacje online', 'Profil zespołu'],
    color: 'from-rose-500 to-rose-600',
    bgColor: 'bg-rose-100 dark:bg-rose-900/30',
    demoUrl: '/demo/style-studio',
    industry: 'Uroda i Wellness',
    tech: ['Next.js', 'Tailwind CSS', 'WCAG AA']
  },
  {
    icon: Car,
    title: 'Warsztat AutoCare Pro',
    description: 'Profesjonalny warsztat samochodowy z pomocą drogową 24/7',
    features: ['System wycen online', 'Pomoc drogowa 24/7', 'Galeria realizacji'],
    color: 'from-orange-500 to-orange-600',
    bgColor: 'bg-orange-100 dark:bg-orange-900/30',
    demoUrl: '/demo/autocare-pro',
    industry: 'Motoryzacja',
    tech: ['Next.js', 'WCAG AA', 'TypeScript']
  },
  {
    icon: Scale,
    title: 'Kancelaria SmallBiz Legal',
    description: 'Profesjonalna kancelaria prawna z bezpłatną konsultacją',
    features: ['Konsultacje prawne', 'Obszary praktyki', 'Zespół prawników'],
    color: 'from-blue-600 to-blue-700',
    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    demoUrl: '/demo/smallbiz-legal',
    industry: 'Usługi prawne',
    tech: ['Next.js', 'WCAG AA', 'TypeScript']
  },
  {
    icon: Dumbbell,
    title: 'FlexFit Studio',
    description: 'Nowoczesna siłownia z zajęciami grupowymi i treningami personalnymi',
    features: ['System rezerwacji zajęć', 'Profile trenerów', 'Pakiety członkowskie', 'Galeria transformacji'],
    color: 'from-red-500 to-orange-500',
    bgColor: 'bg-red-100 dark:bg-red-900/30',
    demoUrl: '/demo/flexfit-studio',
    industry: 'Fitness & Wellness',
    tech: ['Next.js', 'TypeScript', 'WCAG AA', 'Tailwind CSS']
  },
];

export default function Portfolio() {
  // Carousel state
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  // Embla Carousel setup with responsive breakpoints
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      slidesToScroll: 1,
      breakpoints: {
        '(min-width: 768px)': { slidesToScroll: 1 },
        '(min-width: 1024px)': { slidesToScroll: 1 }
      }
    },
    [Autoplay({ delay: 4000, stopOnInteraction: false })]
  );

  // Navigation functions
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);

  const onInit = useCallback((emblaApi: EmblaCarouselType) => {
    setScrollSnaps(emblaApi.scrollSnapList());
  }, []);

  const onSelect = useCallback((emblaApi: EmblaCarouselType) => {
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onInit(emblaApi);
    onSelect(emblaApi);
    emblaApi.on('reInit', onInit);
    emblaApi.on('select', onSelect);
  }, [emblaApi, onInit, onSelect]);



  const scrollToContact = () => {
    // Use the scroll utility for consistent mobile offset behavior
    // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
    scrollToElement('#contact', {
      behavior: 'smooth',
      mobileOffset: 16, // Optimized value - do not change without explicit request
      desktopOffset: 55 // Optimized value - do not change without explicit request
    });
  };

  return (
    <section id="portfolio" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="theme-bg-secondary rounded-2xl p-8">
        <div className="text-center mb-4">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Przykłady realizacji
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-4"></div>
        </div>

        {/* Info Box */}
        <div className="theme-bg-card rounded-2xl p-8 mb-4 text-center">
          <h3 className="text-2xl font-bold theme-text-primary mb-2">
            Projekty demo w akcji
          </h3>
          <p className="text-lg theme-text-muted max-w-3xl mx-auto mb-6">
            Sprawdź w praktyce, jak wyglądają nowoczesne strony internetowe!
            Każdy projekt to <strong>kompletne rozwiązanie</strong> z prawdziwymi funkcjonalnościami.
            Kliknij i zobacz efekty na żywo! 🚀
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <span className="px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium">
              Pełne funkcjonalności
            </span>
            <span className="px-4 py-2 bg-rose-100 text-rose-800 rounded-full text-sm font-medium">
              Responsywny design
            </span>
            <span className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              Gotowe do wdrożenia
            </span>
          </div>
        </div>

        {/* Embla Carousel */}
        <div className="relative mb-16 pb-8 mt-8">
          <div className="embla overflow-hidden rounded-xl" ref={emblaRef}>
            <div className="embla__container flex items-stretch mb-12">
              {demoProjects.map((project, index) => {
                const Icon = project.icon;
                return (
                  <div
                    key={index}
                    className="embla__slide flex-[0_0_100%] md:flex-[0_0_50%] lg:flex-[0_0_33.333%] px-4"
                  >
                    <div className="group theme-bg-card rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden hover:-translate-y-1 h-full flex flex-col">
                      {/* Demo Project Display Area */}
                      <div className={`h-48 ${project.bgColor} relative overflow-hidden`}>
                        {/* Browser Mockup */}
                        <div className="absolute top-4 left-4 right-4 bg-white rounded-lg shadow-sm">
                          <div className="flex items-center px-3 py-2 border-b border-gray-200">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            </div>
                            <div className="flex-1 mx-4 h-4 bg-gray-300 dark:bg-gray-100 rounded"></div>
                          </div>
                          <div className="p-3 space-y-2">
                            <div className="h-3 bg-gray-400 dark:bg-gray-200 rounded w-3/4"></div>
                            <div className="h-2 bg-gray-300 dark:bg-gray-100 rounded w-full"></div>
                            <div className="h-2 bg-gray-300 dark:bg-gray-100 rounded w-2/3"></div>
                          </div>
                        </div>

                        {/* Icon */}
                        <div className={`absolute bottom-4 right-4 w-12 h-12 bg-gradient-to-r ${project.color} rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                          <Icon size={24} />
                        </div>

                        {/* Industry Badge */}
                        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium text-gray-700">
                          {project.industry}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-6 flex-1 flex flex-col">
                        <h3 className="text-xl font-bold theme-text-primary mb-2">
                          {project.title}
                        </h3>
                        <p className="theme-text-muted mb-4">
                          {project.description}
                        </p>

                        {/* Features */}
                        <ul className="space-y-1 mb-4 flex-1">
                          {project.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-center text-sm theme-text-muted">
                              <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>

                        {/* Tech Stack */}
                        <div className="flex flex-wrap gap-1 mb-4">
                          {project.tech.map((tech, techIndex) => (
                            <span key={techIndex} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded-full theme-text-muted">
                              {tech}
                            </span>
                          ))}
                        </div>

                        {/* Demo Link */}
                        <Link
                          href={project.demoUrl}
                          className={`w-full bg-gradient-to-r ${project.color} text-white py-2 px-4 rounded-lg font-medium text-center transition-all duration-300 hover:shadow-lg hover:scale-105 flex items-center justify-center gap-2`}
                        >
                          <ExternalLink size={16} />
                          Zobacz demo na żywo
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-center space-x-4 mt-6">
            <button
              onClick={scrollPrev}
              className="p-3 rounded-full bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-gray-200 dark:border-gray-600"
              aria-label="Poprzedni slajd"
            >
              <ChevronLeft size={20} className="text-gray-600 dark:text-gray-300" />
            </button>

            {/* Dynamic Navigation Dots */}
            <div className="flex items-center justify-center space-x-3 px-4">
              {scrollSnaps.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollTo(index)}
                  className={`relative transition-all duration-300 ease-in-out transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full ${
                    index === selectedIndex
                      ? 'w-4 h-4 p-1'
                      : 'w-2.5 h-2.5 hover:w-3 hover:h-3'
                  }`}
                  aria-label={`Przejdź do slajdu ${index + 1}`}
                  aria-current={index === selectedIndex ? 'true' : 'false'}
                >
                  <div
                    className={`w-full h-full rounded-full transition-all duration-300 ease-in-out ${
                      index === selectedIndex
                        ? 'bg-primary-600 dark:bg-primary-500 shadow-xl ring-2 ring-primary-200 dark:ring-primary-800 ring-offset-1'
                        : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 hover:shadow-md border border-gray-300 dark:border-gray-500'
                    }`}
                  />
                  {/* Enhanced active dot glow effect */}
                  {index === selectedIndex && (
                    <>
                      <div className="absolute inset-0 rounded-full bg-primary-500 opacity-40 animate-pulse" />
                      <div className="absolute inset-0 rounded-full bg-primary-400 opacity-20 scale-150 animate-pulse" style={{ animationDelay: '0.5s' }} />
                    </>
                  )}
                </button>
              ))}
            </div>

            <button
              onClick={scrollNext}
              className="p-3 rounded-full bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-gray-200 dark:border-gray-600"
              aria-label="Następny slajd"
            >
              <ChevronRight size={20} className="text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>

        {/* CTA Section - Enhanced Mobile Centering */}
        <div className="text-center theme-bg-card rounded-2xl shadow-lg p-6 sm:p-8">
          <h3 className="text-xl sm:text-2xl font-bold theme-text-primary mb-4">
            Zostań moim klientem
          </h3>
          <p className="theme-text-muted mb-6 sm:mb-8 max-w-2xl mx-auto px-4 sm:px-0">
            Skorzystaj z wyjątkowej okazji i otrzymaj profesjonalną stronę internetową
            w specjalnej cenie. Moi klienci zawsze otrzymują dodatkowe korzyści i priorytetowe wsparcie.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0">
            <button
              onClick={scrollToContact}
              className="w-full sm:w-auto bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center"
            >
              Skorzystaj z oferty
              <ArrowRight size={20} className="ml-2" />
            </button>

            <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
              Bezpłatna konsultacja • Bez zobowiązań
            </div>
          </div>

          {/* Benefits - Enhanced Mobile Centering */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-4 mt-8 pt-8 border-t theme-border">
            <div className="flex flex-col items-center justify-center text-center px-4">
              <div className="text-2xl sm:text-3xl font-bold theme-text-blue mb-2">-30%</div>
              <div className="text-sm theme-text-muted">Specjalna cena</div>
            </div>
            <div className="flex flex-col items-center justify-center text-center px-4">
              <div className="text-2xl sm:text-3xl font-bold theme-text-purple mb-2">+60</div>
              <div className="text-sm theme-text-muted">Dni wsparcia</div>
            </div>
            <div className="flex flex-col items-center justify-center text-center px-4">
              <div className="text-2xl sm:text-3xl font-bold theme-text-green mb-2">1:1</div>
              <div className="text-sm theme-text-muted">Dedykowane wsparcie</div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
